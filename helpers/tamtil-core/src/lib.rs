//! # TAMTIL Core: A Type-Safe Distributed Actor System
//!
//! ## Abstract
//!
//! This paper presents the core type system for TAMTIL (Typed Actor Model with
//! Transactional Isolated Logs), a distributed actor framework that combines
//! Alice Ryhl's actor pattern with zero-copy serialization and embedded consensus.
//! The type system provides mathematical guarantees for distributed consistency
//! while maintaining developer productivity through a unified programming model.
//!
//! ## 1. Introduction
//!
//! ### 1.1 Problem Statement
//!
//! Modern distributed systems require three fundamental properties:
//! 1. **Type Safety**: Compile-time guarantees for distributed communication
//! 2. **Event Sourcing**: Deterministic state changes for distributed replication
//! 3. **Actor Isolation**: Memory safety and fault tolerance through message passing
//!
//! ### 1.2 Contributions
//!
//! This type system makes the following contributions:
//! 1. **Pure Functional Reactions**: All state changes expressed as memory operations
//! 2. **Scheduling as Event Sourcing**: Time-based execution through memory operations
//! 3. **Hierarchical Actor Addressing**: URL-based actor identification with automatic cleanup
//! 4. **Zero-Copy Type Preservation**: rkyv-based serialization without type erasure
//!
//! ## 2. Core Type System
//!
//! ### 2.1 Fundamental Types
//!
//! The type system is built on four fundamental abstractions that provide
//! mathematical guarantees for distributed actor systems.

use async_trait::async_trait;
use std::marker::PhantomData;
use std::time::{Duration, SystemTime};

// ============================================================================
// SECTION 2.2: IDENTITY AND ADDRESSING
// ============================================================================

/// ## Definition 2.2.1: Node Identifier
///
/// A node identifier represents a unique computational unit in the distributed system.
/// Using u64 provides 2^64 possible nodes while maintaining cache-friendly performance.
///
/// **Valid Range**: All u64 values are valid NodeIds. NodeId(0) is a valid identifier
/// with no special reserved meaning - it can represent a regular node in the system.
pub type NodeId = u64;

// ============================================================================
// SECTION 2.2.1: TYPED IDENTIFIERS FOR ENHANCED TYPE SAFETY
// ============================================================================

/// ## Definition *******: Memory Key
///
/// **Type Safety**: Prevents accidental misuse of memory keys with other string types.
/// **Semantic Clarity**: Makes it clear this string is specifically for memory operations.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct MemoryKey(String);

impl MemoryKey {
    /// Create a new memory key with validation
    pub fn new(key: impl Into<String>) -> Result<Self, TamtilError> {
        let key = key.into();
        if key.is_empty() {
            return Err(TamtilError::Validation {
                message: "Memory key cannot be empty".to_string(),
            });
        }
        Ok(Self(key))
    }

    /// Get the key as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the key as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for MemoryKey {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.1.2: Schedule Identifier
///
/// **Type Safety**: Prevents mixing schedule IDs with other string identifiers.
/// **Semantic Clarity**: Makes it clear this string identifies a scheduled reaction.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ScheduleId(String);

impl ScheduleId {
    /// Create a new schedule ID with validation
    pub fn new(id: impl Into<String>) -> Result<Self, TamtilError> {
        let id = id.into();
        if id.is_empty() {
            return Err(TamtilError::Validation {
                message: "Schedule ID cannot be empty".to_string(),
            });
        }
        Ok(Self(id))
    }

    /// Generate a locally unique schedule ID
    ///
    /// **Uniqueness Guarantee**: Unique within high-resolution timestamp limits on the same node.
    /// For distributed systems, consider incorporating NodeId or using a distributed ID generator.
    ///
    /// **Clock Dependency**: Relies on system clock monotonicity. Clock jumps may affect uniqueness.
    pub fn generate() -> Self {
        use std::time::{SystemTime, UNIX_EPOCH};
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos();
        Self(format!("schedule_{}", timestamp))
    }

    /// Generate a schedule ID with node information for distributed uniqueness
    ///
    /// **Distributed Uniqueness**: Incorporates node_id to ensure uniqueness across nodes.
    pub fn generate_with_node(node_id: NodeId) -> Self {
        use std::time::{SystemTime, UNIX_EPOCH};
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos();
        Self(format!("schedule_{}_{}", node_id, timestamp))
    }

    /// Get the ID as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the ID as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for ScheduleId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.1.3: Visual Identifier
///
/// **Type Safety**: Prevents mixing visual IDs with other string identifiers.
/// **Semantic Clarity**: Makes it clear this string identifies a visual component.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct VisualId(String);

impl VisualId {
    /// Create a new visual ID with validation
    pub fn new(id: impl Into<String>) -> Result<Self, TamtilError> {
        let id = id.into();
        if id.is_empty() {
            return Err(TamtilError::Validation {
                message: "Visual ID cannot be empty".to_string(),
            });
        }
        Ok(Self(id))
    }

    /// Get the ID as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the ID as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for VisualId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.1.4: Action Type Name
///
/// **Type Safety**: Prevents mixing action type names with other strings.
/// **Semantic Clarity**: Makes it clear this string refers to an Action type.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ActionTypeName(String);

impl ActionTypeName {
    /// Create a new action type name with validation
    pub fn new(name: impl Into<String>) -> Result<Self, TamtilError> {
        let name = name.into();
        if name.is_empty() {
            return Err(TamtilError::Validation {
                message: "Action type name cannot be empty".to_string(),
            });
        }
        Ok(Self(name))
    }

    /// Get the name as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the name as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for ActionTypeName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.1.5: Role Name
///
/// **Type Safety**: Prevents mixing role names with other strings.
/// **Security**: Makes authorization code more explicit and type-safe.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct RoleName(String);

impl RoleName {
    /// Create a new role name with validation
    pub fn new(name: impl Into<String>) -> Result<Self, TamtilError> {
        let name = name.into();
        if name.is_empty() {
            return Err(TamtilError::Validation {
                message: "Role name cannot be empty".to_string(),
            });
        }
        Ok(Self(name))
    }

    /// Get the name as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the name as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for RoleName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.1.6: Permission Name
///
/// **Type Safety**: Prevents mixing permission names with other strings.
/// **Security**: Makes authorization code more explicit and type-safe.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct PermissionName(String);

impl PermissionName {
    /// Create a new permission name with validation
    pub fn new(name: impl Into<String>) -> Result<Self, TamtilError> {
        let name = name.into();
        if name.is_empty() {
            return Err(TamtilError::Validation {
                message: "Permission name cannot be empty".to_string(),
            });
        }
        Ok(Self(name))
    }

    /// Get the name as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the name as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for PermissionName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.1.7: Platform Domain
///
/// **Type Safety**: Prevents mixing platform domains with other strings.
/// **Validation**: Could carry validation for domain format.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct PlatformDomain(String);

impl PlatformDomain {
    /// Create a new platform domain with validation
    ///
    /// **Domain Format**: Validates basic domain structure but not full RFC compliance.
    /// For production use, consider using a dedicated domain validation library.
    pub fn new(domain: impl Into<String>) -> Result<Self, TamtilError> {
        let domain = domain.into();

        if domain.is_empty() {
            return Err(TamtilError::Validation {
                message: "Platform domain cannot be empty".to_string(),
            });
        }

        // Basic domain validation - not RFC compliant but catches common issues
        if !domain.contains('.') {
            return Err(TamtilError::Validation {
                message: "Platform domain must contain at least one dot (e.g., 'example.com')".to_string(),
            });
        }

        if domain.starts_with('.') || domain.ends_with('.') {
            return Err(TamtilError::Validation {
                message: "Platform domain cannot start or end with a dot".to_string(),
            });
        }

        if domain.contains("..") {
            return Err(TamtilError::Validation {
                message: "Platform domain cannot contain consecutive dots".to_string(),
            });
        }

        if domain.contains(' ') || domain.contains('\t') || domain.contains('\n') {
            return Err(TamtilError::Validation {
                message: "Platform domain cannot contain whitespace characters".to_string(),
            });
        }

        // Check for basic length constraints
        if domain.len() > 253 {
            return Err(TamtilError::Validation {
                message: "Platform domain cannot exceed 253 characters".to_string(),
            });
        }

        // Validate each label (segment between dots)
        for label in domain.split('.') {
            if label.is_empty() {
                return Err(TamtilError::Validation {
                    message: "Platform domain cannot have empty labels between dots".to_string(),
                });
            }

            if label.len() > 63 {
                return Err(TamtilError::Validation {
                    message: format!("Domain label '{}' cannot exceed 63 characters", label),
                });
            }

            if label.starts_with('-') || label.ends_with('-') {
                return Err(TamtilError::Validation {
                    message: format!("Domain label '{}' cannot start or end with hyphen", label),
                });
            }
        }

        Ok(Self(domain))
    }

    /// Get the domain as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the domain as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for PlatformDomain {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.1.8: Context Name
///
/// **Type Safety**: Prevents mixing context names with other strings.
/// **Semantic Clarity**: Makes it clear this string identifies a context.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ContextName(String);

impl ContextName {
    /// Create a new context name with validation
    pub fn new(name: impl Into<String>) -> Result<Self, TamtilError> {
        let name = name.into();
        if name.is_empty() {
            return Err(TamtilError::Validation {
                message: "Context name cannot be empty".to_string(),
            });
        }
        Ok(Self(name))
    }

    /// Get the name as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the name as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for ContextName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

// ============================================================================
// SECTION 2.2.2: TYPED PAYLOADS FOR ENHANCED CLARITY
// ============================================================================

/// ## Definition 2.2.2.1: Serialized Reaction
///
/// **Type Safety**: Indicates this Vec<u8> contains a serialized reaction.
/// **Semantic Clarity**: Makes it clear this isn't arbitrary bytes but specifically a reaction.
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct SerializedReaction(Vec<u8>);

impl SerializedReaction {
    /// Create a new serialized reaction
    pub fn new(bytes: Vec<u8>) -> Self {
        Self(bytes)
    }

    /// Get the bytes
    pub fn as_bytes(&self) -> &[u8] {
        &self.0
    }

    /// Get the bytes as a vector
    pub fn into_bytes(self) -> Vec<u8> {
        self.0
    }

    /// Get the size in bytes
    pub fn len(&self) -> usize {
        self.0.len()
    }

    /// Check if empty
    pub fn is_empty(&self) -> bool {
        self.0.is_empty()
    }
}

/// ## Definition 2.2.2.2: Serialized Actor State
///
/// **Type Safety**: Indicates this Vec<u8> contains serialized actor state.
/// **Semantic Clarity**: Makes it clear this is initial state data, not arbitrary bytes.
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct SerializedActorState(Vec<u8>);

impl SerializedActorState {
    /// Create a new serialized actor state
    pub fn new(bytes: Vec<u8>) -> Self {
        Self(bytes)
    }

    /// Get the bytes
    pub fn as_bytes(&self) -> &[u8] {
        &self.0
    }

    /// Get the bytes as a vector
    pub fn into_bytes(self) -> Vec<u8> {
        self.0
    }

    /// Get the size in bytes
    pub fn len(&self) -> usize {
        self.0.len()
    }

    /// Check if empty
    pub fn is_empty(&self) -> bool {
        self.0.is_empty()
    }
}

/// ## Definition 2.2.2.3: Memory Value
///
/// **Type Safety**: Indicates this Vec<u8> contains a memory value.
/// **Semantic Clarity**: Makes it clear this is stored memory data.
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct MemoryValue(Vec<u8>);

impl MemoryValue {
    /// Create a new memory value
    pub fn new(bytes: Vec<u8>) -> Self {
        Self(bytes)
    }

    /// Get the bytes
    pub fn as_bytes(&self) -> &[u8] {
        &self.0
    }

    /// Get the bytes as a vector
    pub fn into_bytes(self) -> Vec<u8> {
        self.0
    }

    /// Get the size in bytes
    pub fn len(&self) -> usize {
        self.0.len()
    }

    /// Check if empty
    pub fn is_empty(&self) -> bool {
        self.0.is_empty()
    }
}

// ============================================================================
// SECTION 2.2.3: RESOURCE TYPE ENUMERATION
// ============================================================================

/// ## Definition 2.2.3.1: Resource Type
///
/// **Type Safety**: Instead of using strings for resource types, use a specific enum.
/// **Extensibility**: Marked as non_exhaustive for future resource types.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
#[non_exhaustive]
pub enum ResourceType {
    /// Memory resource
    Memory,
    /// CPU resource
    Cpu,
    /// Network bandwidth resource
    NetworkBandwidth,
    /// Disk I/O resource
    DiskIo,
    /// Actor count resource
    ActorCount,
    /// Custom resource type
    Custom(String),
}

impl std::fmt::Display for ResourceType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ResourceType::Memory => write!(f, "memory"),
            ResourceType::Cpu => write!(f, "cpu"),
            ResourceType::NetworkBandwidth => write!(f, "network_bandwidth"),
            ResourceType::DiskIo => write!(f, "disk_io"),
            ResourceType::ActorCount => write!(f, "actor_count"),
            ResourceType::Custom(name) => write!(f, "custom_{}", name),
        }
    }
}

// ============================================================================
// SECTION 2.2.5: VALIDATION FUNCTION NAME
// ============================================================================

/// ## Definition 2.2.5.1: Validation Function Name
///
/// **Type Safety**: Prevents mixing validation function names with other strings.
/// **Semantic Clarity**: Makes it clear this string refers to a validation function.
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ValidationFunctionName(String);

impl ValidationFunctionName {
    /// Create a new validation function name with validation
    pub fn new(name: impl Into<String>) -> Result<Self, TamtilError> {
        let name = name.into();
        if name.is_empty() {
            return Err(TamtilError::Validation {
                message: "Validation function name cannot be empty".to_string(),
            });
        }
        Ok(Self(name))
    }

    /// Get the name as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get the name as a string
    pub fn into_string(self) -> String {
        self.0
    }
}

impl std::fmt::Display for ValidationFunctionName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// ## Definition 2.2.2: Consensus Ballot
///
/// **Theorem**: Ballots provide total ordering across distributed nodes.
/// **Proof**: The tuple (n, pid) with lexicographic ordering ensures that for any
/// two ballots B1 = (n1, pid1) and B2 = (n2, pid2), exactly one of B1 < B2,
/// B1 > B2, or B1 = B2 holds, satisfying the trichotomy property.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct Ballot {
    /// Ballot number - monotonically increasing
    n: u64,
    /// Proposer node ID for tie-breaking
    pid: NodeId,
}

impl Ballot {
    /// Private constructor - use BallotBuilder instead
    fn new(n: u64, pid: NodeId) -> Self {
        Self { n, pid }
    }

    /// Get the ballot number
    pub fn n(&self) -> u64 {
        self.n
    }

    /// Get the proposer node ID
    pub fn pid(&self) -> NodeId {
        self.pid
    }
}

/// Builder for Ballot with validation and fluent interface
#[derive(Debug, Default)]
pub struct BallotBuilder {
    n: Option<u64>,
    pid: Option<NodeId>,
}

/// Error type for Ballot construction
#[derive(Debug, thiserror::Error)]
#[non_exhaustive]
pub enum BallotBuildError {
    #[error("Ballot number (n) is required")]
    MissingN,
    #[error("Proposer ID (pid) is required")]
    MissingPid,
}

impl BallotBuilder {
    /// Create a new ballot builder
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the ballot number
    pub fn n(mut self, n: u64) -> Self {
        self.n = Some(n);
        self
    }

    /// Set the proposer node ID
    pub fn pid(mut self, pid: NodeId) -> Self {
        self.pid = Some(pid);
        self
    }

    /// Build the ballot with validation
    pub fn build(self) -> Result<Ballot, BallotBuildError> {
        let n = self.n.ok_or(BallotBuildError::MissingN)?;
        let pid = self.pid.ok_or(BallotBuildError::MissingPid)?;
        Ok(Ballot::new(n, pid))
    }
}

/// ## Definition 2.2.3: Hierarchical Actor Identifier
///
/// **Invariant**: Actor IDs form a tree structure where stopping a parent
/// automatically stops all descendants, preventing resource leaks.
///
/// **Format**: `platform.com/context_name/context_id/actor_name/actor_id`
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ActorId {
    /// URL-like hierarchical identifier
    id: String,
}

impl ActorId {
    /// Private constructor - use ActorIdBuilder instead
    fn new(id: String) -> Self {
        Self { id }
    }

    /// Get the actor ID string
    pub fn id_str(&self) -> &str {
        &self.id
    }

    /// Get a cloned copy of the actor ID string
    pub fn id_string(&self) -> String {
        self.id.clone()
    }

    /// Create a child actor ID from this parent ID
    pub fn child(&self, child_name: impl Into<String>) -> ActorId {
        let child_name = child_name.into();
        let hierarchical_id = format!("{}/{}", self.id, child_name);
        ActorId::new(hierarchical_id)
    }

    /// Get the parent ID of this actor
    pub fn parent(&self) -> Option<ActorId> {
        let parts: Vec<&str> = self.id.split('/').collect();
        if parts.len() <= 1 {
            return None;
        }
        let parent_parts = &parts[..parts.len() - 1];
        let parent_id = parent_parts.join("/");
        Some(ActorId::new(parent_id))
    }

    /// Check if this actor is a child of the given parent
    pub fn is_child_of(&self, potential_parent: &ActorId) -> bool {
        self.id.starts_with(&format!("{}/", potential_parent.id))
    }

    /// Get the actor name (last part of the hierarchical ID)
    pub fn name(&self) -> &str {
        self.id.split('/').last().unwrap_or(&self.id)
    }

    // **REMOVED**: get_children(&self, all_actor_ids: &[ActorId]) -> Vec<ActorId>
    //
    // **Problem**: This method required a global list of all actor IDs, which is:
    // - A scalability bottleneck in distributed systems
    // - Impossible to maintain consistently across nodes
    // - A single point of failure for hierarchical cleanup
    //
    // **Solution**: Event-sourced parent-child relationship tracking:
    // - Each parent actor stores its direct children in its own state
    // - MemoryOperation::ChildActorCreated adds children to parent's list
    // - MemoryOperation::ChildActorTerminated removes children from parent's list
    // - Hierarchical stop propagates through parent -> children relationships
    // - No global state required, fully distributed and scalable
    //
    // **Usage**: Actors access their children through their own state, not through
    // ActorId methods. The system handles hierarchical cleanup automatically
    // through the event-sourced parent-child tracking mechanism.
}

impl std::fmt::Display for ActorId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.id)
    }
}

impl Default for Ballot {
    /// Create a default ballot with n=0 and pid=0
    ///
    /// **Usage**: Primarily for initialization. In consensus algorithms,
    /// actual ballots should use meaningful values through BallotBuilder.
    fn default() -> Self {
        Self { n: 0, pid: 0 }
    }
}

/// Builder for ActorId with validation and fluent interface
#[derive(Debug, Default)]
pub struct ActorIdBuilder {
    id: Option<String>,
}

/// Error type for ActorId construction
#[derive(Debug, thiserror::Error)]
#[non_exhaustive]
pub enum ActorIdBuildError {
    #[error("Actor ID string is required")]
    MissingId,
    #[error("Actor ID cannot be empty")]
    EmptyId,
    #[error("Invalid actor ID format: {reason}")]
    InvalidFormat { reason: String },
}

impl ActorIdBuilder {
    /// Create a new actor ID builder
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the actor ID string
    pub fn id(mut self, id: impl Into<String>) -> Self {
        self.id = Some(id.into());
        self
    }

    /// Build from a parent actor ID and child name
    pub fn child_of(parent: &ActorId, child_name: impl Into<String>) -> Self {
        let child_name = child_name.into();
        let hierarchical_id = format!("{}/{}", parent.id_str(), child_name);
        Self {
            id: Some(hierarchical_id),
        }
    }

    /// Build the actor ID with validation
    pub fn build(self) -> Result<ActorId, ActorIdBuildError> {
        let id = self.id.ok_or(ActorIdBuildError::MissingId)?;

        if id.is_empty() {
            return Err(ActorIdBuildError::EmptyId);
        }

        // Validate hierarchical format: platform.com/context_name/context_id/actor_name/actor_id
        Self::validate_hierarchical_format(&id)?;

        Ok(ActorId::new(id))
    }

    /// Validate the hierarchical format of an actor ID
    fn validate_hierarchical_format(id: &str) -> Result<(), ActorIdBuildError> {
        // Check for forbidden characters
        if id.contains("//") {
            return Err(ActorIdBuildError::InvalidFormat {
                reason: "Actor ID cannot contain consecutive slashes".to_string(),
            });
        }

        if id.starts_with('/') || id.ends_with('/') {
            return Err(ActorIdBuildError::InvalidFormat {
                reason: "Actor ID cannot start or end with slash".to_string(),
            });
        }

        // Split into segments and validate each
        let segments: Vec<&str> = id.split('/').collect();

        // Must have at least domain part
        if segments.is_empty() {
            return Err(ActorIdBuildError::InvalidFormat {
                reason: "Actor ID must have at least a domain".to_string(),
            });
        }

        // Validate each segment is non-empty
        for (i, segment) in segments.iter().enumerate() {
            if segment.is_empty() {
                return Err(ActorIdBuildError::InvalidFormat {
                    reason: format!("Segment {} cannot be empty", i + 1),
                });
            }

            // Check for forbidden characters in segments
            if segment.contains(' ') || segment.contains('\t') || segment.contains('\n') {
                return Err(ActorIdBuildError::InvalidFormat {
                    reason: format!("Segment '{}' contains forbidden whitespace characters", segment),
                });
            }
        }

        // First segment should look like a domain (contain at least one dot)
        if !segments[0].contains('.') {
            return Err(ActorIdBuildError::InvalidFormat {
                reason: "First segment must be a domain (contain at least one dot)".to_string(),
            });
        }

        Ok(())
    }
}

// ============================================================================
// SECTION 2.2.4: STABLE WIRE FORMAT DEFINITIONS
// ============================================================================

/// ## Definition 2.2.4.1: Stable Wire Format for Ballot
///
/// **Wire Format Stability**: This archived representation is guaranteed to remain
/// stable across all versions. Internal Ballot struct can change freely without
/// affecting serialized data compatibility.
///
/// **Endianness**: Uses explicit little-endian encoding for strict cross-platform
/// compatibility. This ensures the wire format is identical regardless of the
/// target architecture's native endianness.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
#[repr(C)]
pub struct ArchivedBallot {
    /// Ballot number in little-endian format
    n: rkyv::rend::u64_le,
    /// Proposer node ID in little-endian format
    pid: rkyv::rend::u64_le,
}

// Mark as safe for zero-copy operations
unsafe impl rkyv::traits::Portable for ArchivedBallot {}
unsafe impl rkyv::traits::NoUndef for ArchivedBallot {}

impl ArchivedBallot {
    /// Get the ballot number
    pub fn n(&self) -> u64 {
        self.n.to_native()
    }

    /// Get the proposer node ID
    pub fn pid(&self) -> NodeId {
        self.pid.to_native()
    }
}

/// ## Definition 2.2.4.2: Stable Wire Format for ActorId
///
/// **Wire Format Stability**: Uses rkyv's ArchivedString which provides a stable
/// wire format for string data with relative pointers.
#[derive(Debug)]
#[repr(transparent)]
pub struct ArchivedActorId {
    /// String data with stable wire format
    id: rkyv::string::ArchivedString,
}

// Mark as safe for zero-copy operations
unsafe impl rkyv::traits::Portable for ArchivedActorId {}

/// ## Implementation Note: Method Duplication
///
/// **Design Decision**: ArchivedActorId duplicates methods from ActorId to enable
/// direct operations on archived data without full deserialization. This is a
/// conscious trade-off between performance and maintainability.
///
/// **Maintenance Consideration**: If the logic for hierarchical operations changes,
/// both ActorId and ArchivedActorId implementations must be updated. The unwrap()
/// calls are safe because archived data is assumed to be valid.
impl ArchivedActorId {
    /// Get the actor ID string
    pub fn id_str(&self) -> &str {
        self.id.as_str()
    }

    /// Get a cloned copy of the actor ID string
    pub fn id_string(&self) -> String {
        self.id.as_str().to_string()
    }

    /// Create a child actor ID from this parent ID
    ///
    /// # Errors
    /// Returns an error if the child_name is invalid (empty, contains forbidden characters, etc.)
    pub fn child(&self, child_name: impl Into<String>) -> Result<ActorId, ActorIdBuildError> {
        let child_name = child_name.into();

        // Validate child name before creating hierarchical ID
        if child_name.is_empty() {
            return Err(ActorIdBuildError::InvalidFormat {
                reason: "Child name cannot be empty".to_string(),
            });
        }

        if child_name.contains('/') {
            return Err(ActorIdBuildError::InvalidFormat {
                reason: "Child name cannot contain slashes".to_string(),
            });
        }

        let hierarchical_id = format!("{}/{}", self.id.as_str(), child_name);
        ActorIdBuilder::new().id(hierarchical_id).build()
    }

    /// Get the parent ID of this actor
    ///
    /// # Errors
    /// Returns None if this is a root actor, or an error if parent ID construction fails
    pub fn parent(&self) -> Result<Option<ActorId>, ActorIdBuildError> {
        let parts: Vec<&str> = self.id.as_str().split('/').collect();
        if parts.len() <= 1 {
            return Ok(None);
        }
        let parent_parts = &parts[..parts.len() - 1];
        let parent_id = parent_parts.join("/");
        Ok(Some(ActorIdBuilder::new().id(parent_id).build()?))
    }

    /// Check if this actor is a child of the given parent
    pub fn is_child_of(&self, potential_parent: &ArchivedActorId) -> bool {
        self.id.as_str().starts_with(&format!("{}/", potential_parent.id.as_str()))
    }

    /// Get the actor name (last part of the hierarchical ID)
    pub fn name(&self) -> &str {
        self.id.as_str().split('/').last().unwrap_or(self.id.as_str())
    }
}

impl std::fmt::Display for ArchivedActorId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.id.as_str())
    }
}

// ============================================================================
// SECTION 2.3: ERROR HANDLING AND RESULTS
// ============================================================================

/// ## Definition 2.3.1: System Error Types
///
/// **Coverage Goal**: This enumeration covers known and anticipated failure modes
/// in a distributed actor system. Marked as #[non_exhaustive] to allow for future
/// error types as the system evolves.
#[derive(Debug, thiserror::Error)]
#[non_exhaustive]
pub enum TamtilError {
    #[error("Serialization failed: {context}")]
    Serialization { context: String },

    #[error("Deserialization failed: {context}")]
    Deserialization { context: String },

    #[error("Consensus timeout: {operation}")]
    ConsensusTimeout { operation: String },

    #[error("Platform initialization failed: {reason}")]
    PlatformInitFailed { reason: String },

    #[error("Actor not found: {actor_id}")]
    ActorNotFound { actor_id: String },

    #[error("Context not found: {context_id}")]
    ContextNotFound { context_id: String },

    #[error("Storage error: {message}")]
    Storage { message: String },

    #[error("Network error: {message}")]
    Network { message: String },

    #[error("Validation failed: {message}")]
    Validation { message: String },
}

/// ## Definition 2.3.2: Result Type
///
/// Standard result type providing consistent error handling across the system.
pub type TamtilResult<T> = Result<T, TamtilError>;

// ============================================================================
// SECTION 2.4: EVENT SOURCING PRIMITIVES
// ============================================================================

/// ## Definition 2.4.1: Atomic Memory Operations
///
/// **ACID Property**: The TAMTIL system ensures all operations in a single vector are applied atomically.
/// **Commutativity**: Operations on different keys commute when processed by the system, enabling parallel execution.
/// **Idempotency**: The TAMTIL system ensures reapplying the same operation sequence produces identical results.
/// **Hierarchical Lifecycle**: Includes operations for tracking parent-child relationships
/// to enable distributed hierarchical cleanup without global state.
///
/// **Note**: These properties are guarantees provided by the TAMTIL system when processing
/// these operations, not inherent properties of the enum variants themselves.
#[derive(Debug, Clone)]
#[non_exhaustive]
pub enum MemoryOperation {
    /// Set a key-value pair
    Set {
        key: MemoryKey,
        value: MemoryValue
    },
    /// Delete a key
    Delete {
        key: MemoryKey
    },
    /// Increment a counter (commutative)
    Increment {
        key: MemoryKey,
        amount: i64
    },
    /// Append to a list (order-preserving)
    Append {
        key: MemoryKey,
        value: MemoryValue
    },
    /// Remove from a list by index
    Remove {
        key: MemoryKey,
        index: usize
    },

    /// Schedule a reaction for future execution
    ///
    /// **Type Resolution**: The reaction_type_name enables the system to deserialize
    /// reaction_bytes back to the correct ScheduledReaction implementation when triggered.
    Schedule {
        schedule_id: ScheduleId,
        reaction_bytes: SerializedReaction,
        reaction_type_name: String, // Fully qualified type name for deserialization
        schedule: Schedule,
        actor_id: ActorId,
    },
    /// Cancel a scheduled reaction
    CancelSchedule {
        schedule_id: ScheduleId
    },

    /// Register a child actor with its parent
    ///
    /// **Event-Sourced Hierarchy**: When an actor creates a child, this operation
    /// adds the child to the parent's list of direct children. This enables
    /// distributed hierarchical cleanup without requiring global actor lists.
    ///
    /// **Atomicity**: Should be part of the same atomic operation set as child
    /// actor creation to prevent inconsistencies.
    ChildActorCreated {
        parent_id: ActorId,
        child_id: ActorId,
    },

    /// Unregister a child actor from its parent
    ///
    /// **Cleanup Tracking**: When a child actor terminates (gracefully or due to
    /// crash detection), this operation removes it from the parent's children list.
    /// Generated automatically by the system or during graceful shutdown.
    ///
    /// **Orphan Prevention**: Critical for preventing resource leaks in distributed
    /// hierarchical actor systems.
    ChildActorTerminated {
        parent_id: ActorId,
        child_id: ActorId,
    },

    /// Store visual definition for UI components
    ///
    /// **Visual Memory**: Special memory operation for storing visual components
    /// that are automatically synchronized with connected clients.
    StoreVisual {
        visual_id: VisualId,
        definition: VisualDefinition,
    },
}

/// ## Definition 2.4.2: Temporal Scheduling
///
/// **Determinism Property**: Schedule definitions are deterministically replicated
/// via the event log. All nodes store identical SystemTime values for any given
/// scheduled event. However, the *interpretation* of when to trigger based on
/// SystemTime requires a leader-based scheduler to handle clock skew.
///
/// **Leader-Based Execution**: A dedicated scheduler leader compares stored
/// SystemTime values against its own NTP-synchronized clock to determine when
/// to trigger scheduled reactions. This provides consistent triggering despite
/// clock skew across worker nodes.
///
/// **User-Friendly API**: Retains familiar SystemTime for absolute scheduling
/// (e.g., "run at 2023-12-25 09:00:00 UTC") while handling distributed
/// consistency through the scheduler architecture.
#[derive(Debug, Clone, PartialEq, Eq)]
#[non_exhaustive]
pub enum Schedule {
    /// Execute once at a specific wall-clock time
    ///
    /// **Leader Interpretation**: Scheduler leader triggers when its local
    /// SystemTime >= stored `at` value. Clock skew is handled by having a
    /// single authoritative timekeeper.
    Once {
        at: SystemTime
    },

    /// Execute repeatedly at fixed intervals
    ///
    /// **Relative Timing**: `every` Duration is robust against clock skew.
    /// **Absolute Anchoring**: `start_at` requires leader interpretation.
    /// **Termination**: `end_at` checked by scheduler leader's clock.
    Interval {
        every: Duration,
        start_at: Option<SystemTime>,
        end_at: Option<SystemTime>,
    },

    /// Execute using cron-like expressions
    ///
    /// **Cron Evaluation**: Scheduler leader evaluates cron expressions
    /// against its local time to determine next execution time.
    /// **Termination**: `end_at` checked by scheduler leader's clock.
    Cron {
        expression: String,
        end_at: Option<SystemTime>,
    },
}

/// ## Definition 2.4.3: Schedule Builder with Distributed Timing Semantics
///
/// **Leader-Based Timing**: All absolute time schedules (using SystemTime) are
/// interpreted by a dedicated scheduler leader that compares stored times against
/// its NTP-synchronized clock. This handles clock skew across distributed nodes.
///
/// **Event Log Storage**: Schedule definitions are stored in the event log,
/// ensuring all nodes have identical schedule data. The leader reads from this
/// log to determine what to trigger and when.
///
/// **Fault Tolerance**: If the scheduler leader fails, another node takes over,
/// re-reads active schedules from the persistent log, and continues monitoring.
/// There may be brief delays during failover.
#[derive(Debug)]
pub struct ScheduleBuilder {
    schedule_type: ScheduleType,
}

#[derive(Debug)]
enum ScheduleType {
    Once { at: SystemTime },
    Interval {
        every: Duration,
        start_at: Option<SystemTime>,
        end_at: Option<SystemTime>,
    },
    Cron {
        expression: String,
        end_at: Option<SystemTime>,
    },
}

/// Error type for Schedule construction
#[derive(Debug, thiserror::Error)]
#[non_exhaustive]
pub enum ScheduleBuildError {
    #[error("Invalid cron expression: {expression}")]
    InvalidCronExpression { expression: String },
    #[error("Interval duration must be positive")]
    InvalidInterval,
    #[error("End time must be after start time")]
    InvalidTimeRange,
}

impl ScheduleBuilder {
    /// Create a one-time schedule for immediate execution
    ///
    /// **Timing Semantics**: Captures SystemTime::now() at creation time.
    /// The scheduler leader will trigger when its clock >= this stored time.
    /// Due to processing delays and leader scheduling, "immediate" means
    /// "as soon as the scheduler leader processes this schedule."
    pub fn now() -> Self {
        Self {
            schedule_type: ScheduleType::Once {
                at: SystemTime::now(),
            },
        }
    }

    /// Create a one-time schedule for execution after a delay
    ///
    /// **Timing Semantics**: Calculates SystemTime::now() + delay at creation.
    /// The scheduler leader triggers when its clock >= the calculated time.
    /// Clock skew between creation node and scheduler leader may cause
    /// slight timing variations (typically within NTP sync tolerance).
    pub fn after(delay: Duration) -> Self {
        Self {
            schedule_type: ScheduleType::Once {
                at: SystemTime::now() + delay,
            },
        }
    }

    /// Create a one-time schedule for execution at a specific time
    ///
    /// **Timing Semantics**: Stores the exact SystemTime provided.
    /// The scheduler leader triggers when its clock >= this time.
    /// Most deterministic option as it doesn't depend on creation time.
    pub fn at(time: SystemTime) -> Self {
        Self {
            schedule_type: ScheduleType::Once { at: time },
        }
    }

    /// Create an interval schedule starting now
    pub fn every(interval: Duration) -> Self {
        Self {
            schedule_type: ScheduleType::Interval {
                every: interval,
                start_at: None,
                end_at: None,
            },
        }
    }

    /// Create an interval schedule with specific start time
    pub fn every_starting_at(interval: Duration, start_at: SystemTime) -> Self {
        Self {
            schedule_type: ScheduleType::Interval {
                every: interval,
                start_at: Some(start_at),
                end_at: None,
            },
        }
    }

    /// Create a cron-based schedule
    pub fn cron(expression: impl Into<String>) -> Self {
        Self {
            schedule_type: ScheduleType::Cron {
                expression: expression.into(),
                end_at: None,
            },
        }
    }

    /// Create a daily schedule at specific hour
    pub fn daily_at_hour(hour: u8) -> Self {
        Self::cron(format!("0 {} * * *", hour))
    }

    /// Create a weekly schedule
    pub fn weekly_on_day(day: u8, hour: u8) -> Self {
        Self::cron(format!("0 {} * * {}", hour, day))
    }

    /// Add an end time to any schedule
    pub fn until(mut self, end_time: SystemTime) -> Self {
        match &mut self.schedule_type {
            ScheduleType::Once { .. } => self, // One-time schedules ignore end time
            ScheduleType::Interval { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
            ScheduleType::Cron { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
        }
    }

    /// Build the schedule with validation
    pub fn build(self) -> Result<Schedule, ScheduleBuildError> {
        match self.schedule_type {
            ScheduleType::Once { at } => Ok(Schedule::Once { at }),
            ScheduleType::Interval {
                every,
                start_at,
                end_at,
            } => {
                if every.is_zero() {
                    return Err(ScheduleBuildError::InvalidInterval);
                }

                // Validate time range if both start and end are specified
                if let (Some(start), Some(end)) = (start_at, end_at) {
                    if end <= start {
                        return Err(ScheduleBuildError::InvalidTimeRange);
                    }
                }

                Ok(Schedule::Interval {
                    every,
                    start_at,
                    end_at,
                })
            }
            ScheduleType::Cron { expression, end_at } => {
                // Basic cron validation - in a real implementation, you'd use a cron parsing library
                if expression.trim().is_empty() {
                    return Err(ScheduleBuildError::InvalidCronExpression {
                        expression: expression.clone(),
                    });
                }

                Ok(Schedule::Cron { expression, end_at })
            }
        }
    }
}

// ============================================================================
// SECTION 2.4.4: DISTRIBUTED SCHEDULER ARCHITECTURE
// ============================================================================

/// ## Definition 2.4.4: Scheduler Leader Architecture
///
/// **Problem Statement**: SystemTime-based schedules in distributed systems
/// face clock skew issues. Different nodes may trigger the same schedule at
/// different physical times, breaking determinism guarantees.
///
/// **Solution**: Leader-based interpretation where a single logical entity
/// (scheduler leader) makes timing decisions for all absolute schedules.
///
/// **Key Properties**:
/// - Schedule definitions are deterministically replicated via event log
/// - All nodes store identical SystemTime values for schedules
/// - Scheduler leader interprets these times against its NTP-synchronized clock
/// - Single point of timing authority prevents inconsistent triggers
/// - Fault tolerance through leader failover with log replay
///
/// **Implementation Notes**:
/// - Scheduler leader monitors active schedules from the event log
/// - For absolute times: triggers when leader's clock >= stored SystemTime
/// - For intervals: uses Duration offsets which are robust against clock skew
/// - Leader should be NTP-synchronized for accuracy
/// - Sharding possible for high-volume scheduling (by schedule_id hash)
///
/// **Trade-offs**:
/// - ✅ Consistent triggering across distributed system
/// - ✅ User-friendly SystemTime API for absolute scheduling
/// - ✅ Leverages event sourcing for schedule persistence
/// - ⚠️ Scheduler leader can become bottleneck (mitigated by sharding)
/// - ⚠️ Brief delays possible during leader failover
/// - ⚠️ Accuracy depends on leader's NTP synchronization
pub struct SchedulerLeader {
    // Implementation details hidden - this is a conceptual type
    // Real implementation would be in separate scheduler module
}

impl SchedulerLeader {
    /// Monitor and trigger schedules based on leader's clock
    ///
    /// **Algorithm**:
    /// 1. Read active schedules from event log
    /// 2. Compare SystemTime values against local clock
    /// 3. Trigger ScheduledReaction::react() for due schedules
    /// 4. Handle interval scheduling with Duration offsets
    /// 5. Evaluate cron expressions against local time
    pub async fn monitor_schedules(&self) -> TamtilResult<()> {
        // Implementation would:
        // - Scan event log for MemoryOperation::Schedule entries
        // - Build in-memory schedule registry
        // - Run timing loop comparing stored times to SystemTime::now()
        // - Send trigger messages to target actors
        // - Handle failover by re-reading log state
        todo!("SchedulerLeader::monitor_schedules implementation")
    }

    /// Handle failover by re-reading schedule state from event log
    ///
    /// **Failover Algorithm**:
    /// 1. Scan event log for all MemoryOperation::Schedule entries
    /// 2. Rebuild in-memory schedule registry
    /// 3. Check for schedules that should have triggered during downtime
    /// 4. Decide whether to trigger missed schedules immediately or skip them
    /// 5. Resume normal monitoring loop
    pub async fn handle_failover(&self) -> TamtilResult<()> {
        // Implementation would handle the complex logic of:
        // - Determining what schedules were missed during leader downtime
        // - Applying business rules for missed schedule handling
        // - Preventing duplicate triggers during leadership transitions
        todo!("SchedulerLeader::handle_failover implementation")
    }
}

// ============================================================================
// SECTION 2.4.5: HIERARCHICAL ACTOR LIFECYCLE MANAGEMENT
// ============================================================================

/// ## Definition 2.4.5: Hierarchical Actor Lifecycle Operations
///
/// **Event-Sourced Hierarchy**: Provides types and operations for managing
/// parent-child relationships in distributed actor systems without requiring
/// global state or centralized registries.
///
/// **Scalable Cleanup**: Enables automatic hierarchical cleanup that works
/// efficiently across distributed nodes through event sourcing.

/// ## Definition *******: Actor Creation Workflow
///
/// **Atomic Creation**: When an actor creates a child, both the child's
/// initialization and parent-child relationship registration happen atomically.
///
/// **Workflow**:
/// 1. Parent actor P decides to create child C
/// 2. System generates hierarchical ActorId for C (child of P)
/// 3. Atomic MemoryOperation set:
///    - Initialize C's state
///    - MemoryOperation::ChildActorCreated { parent_id: P, child_id: C }
/// 4. P's state now includes C in its children list
/// 5. C begins processing actions
pub struct ActorCreationWorkflow {
    /// Parent actor that is creating the child
    pub parent_id: ActorId,
    /// Child actor being created
    pub child_id: ActorId,
    /// Initial state for the child actor
    pub initial_state: SerializedActorState,
}

/// ## Definition *******: Actor Termination Workflow
///
/// **Graceful Termination**: Handles both graceful shutdown and crash detection
/// with automatic parent notification and hierarchical cleanup.
///
/// **Workflow**:
/// 1. Child actor C terminates (graceful or crash detected)
/// 2. System generates MemoryOperation::ChildActorTerminated { parent_id: P, child_id: C }
/// 3. P's state removes C from its children list
/// 4. Resources are cleaned up
pub struct ActorTerminationWorkflow {
    /// Parent actor to notify
    pub parent_id: ActorId,
    /// Child actor that terminated
    pub child_id: ActorId,
    /// Reason for termination
    pub termination_reason: TerminationReason,
}

/// ## Definition 2.4.5.3: Termination Reasons
///
/// **Audit Trail**: Tracks why actors terminate for debugging and monitoring.
#[derive(Debug, Clone, PartialEq, Eq)]
#[non_exhaustive]
pub enum TerminationReason {
    /// Actor terminated gracefully
    Graceful,
    /// Actor crashed due to unhandled error
    Crashed { error: String },
    /// Actor was stopped by its parent
    StoppedByParent,
    /// Actor was stopped by system administrator
    StoppedByAdmin,
    /// Actor exceeded resource limits
    ResourceLimitExceeded { resource: ResourceType },
    /// Actor was idle for too long
    IdleTimeout,
}

/// ## Definition 2.4.5.4: Hierarchical Stop Operation
///
/// **Recursive Cleanup**: Defines the operation for stopping an actor and all
/// its descendants in a distributed system.
///
/// **Algorithm**:
/// 1. Send GracefulStop message to target actor
/// 2. Actor reads its children list from its state
/// 3. Actor sends GracefulStop to each direct child
/// 4. Actor waits for children to confirm termination (with timeout)
/// 5. Actor performs its own cleanup
/// 6. Actor signals completion to its parent
/// 7. Parent removes actor from its children list
pub struct HierarchicalStopOperation {
    /// Actor to stop (may have children)
    pub target_actor: ActorId,
    /// Maximum time to wait for graceful shutdown
    pub graceful_timeout: Duration,
    /// Whether to force stop if graceful timeout expires
    pub force_if_timeout: bool,
}

/// ## Definition 2.4.5.5: Actor State with Children Tracking
///
/// **State Structure**: Defines how actors store their direct children in
/// their event-sourced state for hierarchical lifecycle management.
///
/// **Usage**: Actors include this in their state to track children.
#[derive(Debug, Clone)]
pub struct ActorChildrenState {
    /// Set of direct child actor IDs
    pub direct_children: std::collections::HashSet<ActorId>,
    /// Timestamp when each child was created
    pub child_creation_times: std::collections::HashMap<ActorId, SystemTime>,
    /// Optional parent ID for reverse lookup
    pub parent_id: Option<ActorId>,
}

impl ActorChildrenState {
    /// Create new empty children state
    pub fn new() -> Self {
        Self {
            direct_children: std::collections::HashSet::new(),
            child_creation_times: std::collections::HashMap::new(),
            parent_id: None,
        }
    }

    /// Create children state with known parent
    pub fn with_parent(parent_id: ActorId) -> Self {
        Self {
            direct_children: std::collections::HashSet::new(),
            child_creation_times: std::collections::HashMap::new(),
            parent_id: Some(parent_id),
        }
    }

    /// Add a child to this actor's state
    pub fn add_child(&mut self, child_id: ActorId) {
        self.direct_children.insert(child_id.clone());
        self.child_creation_times.insert(child_id, SystemTime::now());
    }

    /// Remove a child from this actor's state
    pub fn remove_child(&mut self, child_id: &ActorId) {
        self.direct_children.remove(child_id);
        self.child_creation_times.remove(child_id);
    }

    /// Get all direct children
    pub fn children(&self) -> impl Iterator<Item = &ActorId> {
        self.direct_children.iter()
    }

    /// Check if this actor has any children
    pub fn has_children(&self) -> bool {
        !self.direct_children.is_empty()
    }

    /// Get the number of direct children
    pub fn child_count(&self) -> usize {
        self.direct_children.len()
    }
}

impl Default for ActorChildrenState {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// SECTION 2.5: ACTOR MEMORY ABSTRACTION
// ============================================================================

/// ## Definition 2.5.1: Actor Memory Interface
///
/// **Isolation Property**: Each actor can only access its own memory through
/// this interface, ensuring memory safety in concurrent environments.
///
/// **Read-Only Invariant**: Direct state mutation is impossible; all changes
/// must flow through the reaction system via `remember()` operations.
pub struct ActorMemories {
    // Implementation details are intentionally hidden to maintain abstraction
}

// ============================================================================
// SECTION 2.6: SUBSCRIPTION AND COMMUNICATION PRIMITIVES
// ============================================================================

/// ## Definition 2.6.1: Actor Subscription System
///
/// **Publish-Subscribe Pattern**: Actors can subscribe to reactions from other actors,
/// enabling loose coupling and event-driven communication patterns.
///
/// **Type Safety**: Subscriptions are type-safe - actors can only subscribe to
/// reactions they can handle through the RemoteReaction trait.
///
/// **Network Transparency**: Subscriptions work seamlessly across network boundaries,
/// with the system handling serialization, routing, and delivery automatically.

// ============================================================================
// SECTION 2.7: UNIFIED CONTEXT INTERFACE
// ============================================================================

/// ## Definition 2.7.1: Actor System Context
///
/// **Unification Theorem**: The Context provides a single interface for all
/// actor operations, eliminating the need for multiple communication patterns.
///
/// **Capability Security**: Actors can only perform operations they have
/// explicit capability for through the context interface.
pub struct Context {
    /// Memory access for the current actor
    pub memories: ActorMemories,
}

/// ## Definition 2.7.2: Action Builder Pattern
///
/// **Fluent Interface**: Provides type-safe method chaining for actor communication.
/// **Compile-Time Safety**: Invalid operation sequences are caught at compile time.
pub struct ActionBuilder<A: Action> {
    /// Type parameter ensures action-reaction type safety
    _phantom: PhantomData<A>,
}

// ============================================================================
// SECTION 2.8: BEHAVIORAL CONTRACTS (TRAITS)
// ============================================================================

/// ## Definition 2.8.1: Action Contract
///
/// **Pure Function Principle**: Actions should behave as pure functions that produce reactions
/// based solely on their input and the current context state. This is a design principle
/// enforced by convention and code review, not by the Rust type system.
///
/// **Determinism Goal**: Given the same action and context state, the same reaction
/// should be produced, enabling distributed consensus. Implementations must avoid
/// non-deterministic operations (random numbers, current time, external I/O) in act().
///
/// **Side Effect Restriction**: While the type system cannot prevent side effects,
/// implementations should avoid them. All state changes must be expressed through
/// the returned response and subsequent memory operations.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated response type ensuring type safety
    type Response: Send + Sync + 'static;

    /// Execute the action with access to the unified context
    ///
    /// **Purity Guideline**: Implementations should avoid side effects and non-deterministic
    /// operations. All state changes should be expressed through the returned response.
    ///
    /// **Determinism**: For the same input and context state, this should produce the same result.
    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Response>;

    /// Validate this action for security and business rules
    ///
    /// **Security Property**: This method enables capability-based security
    /// and role-based access control at the type system level.
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}

/// ## Definition 2.8.2: Reaction Contract
///
/// **Event Sourcing Theorem**: All state changes in the system are expressed
/// as sequences of memory operations, enabling perfect state reconstruction.
///
/// **Atomicity**: All operations returned by `remember()` are applied atomically.
pub trait Reaction: Send + Sync + 'static {
    /// Define the memory operations this reaction performs
    ///
    /// **Functional Purity**: This method must be pure - same input always
    /// produces the same sequence of memory operations.
    ///
    /// **Commutativity**: Operations on different keys can be reordered without
    /// affecting the final state, enabling distributed optimization.
    fn remember(&self) -> Vec<MemoryOperation>;
}

/// ## Definition 2.8.3: Scheduled Reaction Contract
///
/// **Temporal Consistency**: Scheduled reactions maintain the same event sourcing
/// properties as regular reactions while adding temporal execution semantics.
///
/// **Lifecycle Management**: The system automatically manages actor lifecycle
/// for scheduled execution, ensuring reactions execute even when actors are stopped.
///
/// **Type Resolution**: Implementations must provide a type name for serialization/deserialization.
#[async_trait]
pub trait ScheduledReaction: Send + Sync + 'static {
    /// Get the fully qualified type name for this scheduled reaction
    ///
    /// **Type Registry**: This name is used by the system to deserialize
    /// SerializedReaction bytes back to the correct concrete type when triggered.
    ///
    /// **Convention**: Use module::TypeName format (e.g., "myapp::reactions::DailyBackup")
    fn type_name() -> &'static str where Self: Sized;

    /// Get the type name for this instance (for trait objects)
    fn instance_type_name(&self) -> &'static str;
    /// Define state changes for scheduled execution
    ///
    /// **Scheduling Invariant**: To schedule a reaction, include
    /// `MemoryOperation::Schedule` in the returned vector. This ensures
    /// scheduling follows the same event sourcing pattern as all other operations.
    fn remember(&self) -> Vec<MemoryOperation>;

    /// React to scheduled execution with full actor capabilities
    ///
    /// **Capability Preservation**: Scheduled reactions have the same capabilities
    /// as regular actions, including creating child actors and communicating
    /// with other actors in the system.
    async fn react(&self, ctx: &Context) -> TamtilResult<()>;
}

/// ## Definition 2.8.4: Remote Reaction Contract
///
/// **Distributed Communication**: Remote reactions handle incoming reactions from
/// other actors in the distributed system, enabling cross-actor communication
/// through the subscription mechanism.
///
/// **Network Transparency**: Remote reactions abstract away network details,
/// making distributed communication appear as local method calls.
///
/// **Serialization Safety**: Remote reactions work with rkyv-serialized data,
/// maintaining type safety across network boundaries.
#[async_trait]
pub trait RemoteReaction: Send + Sync + 'static {
    /// Validate incoming remote reaction data
    ///
    /// **Security Property**: This method enables validation of remote reactions
    /// before they are processed, preventing malicious or malformed data from
    /// affecting the actor's state.
    ///
    /// **Type Safety**: Validates that the serialized data can be safely
    /// deserialized into the expected type.
    fn validate(&self, reaction_bytes: &[u8]) -> TamtilResult<()>;

    /// Define state changes for remote reaction processing
    ///
    /// **Event Sourcing Consistency**: Remote reactions follow the same event
    /// sourcing pattern as local reactions, ensuring distributed consistency.
    ///
    /// **Atomicity**: All operations are applied atomically, maintaining
    /// ACID properties across distributed state changes.
    fn remember(&self) -> Vec<MemoryOperation>;

    /// React to incoming remote reaction with full actor capabilities
    ///
    /// **Distributed Capabilities**: Remote reactions can trigger actions on
    /// other actors, create child actors, and perform any operation available
    /// to local reactions.
    ///
    /// **Subscription Handling**: This method is automatically called when
    /// a subscribed reaction arrives from another actor in the system.
    async fn react(&self, ctx: &Context) -> TamtilResult<()>;
}

/// ## Definition 2.8.5: Actor Lifecycle Contract
///
/// **Identity Property**: Each actor has a unique identifier that remains
/// constant throughout its lifecycle.
///
/// **Hierarchical Cleanup**: When an actor stops, all its descendants are
/// automatically stopped, preventing resource leaks.
pub trait Actor: Send + Sync + 'static {
    /// Immutable reference to this actor's unique identifier
    fn id(&self) -> &ActorId;
}

// ============================================================================
// SECTION 2.9: CUSTOM RKYV IMPLEMENTATIONS FOR WIRE FORMAT STABILITY
// ============================================================================

/// ## Implementation 2.9.1: Custom Archive Implementation for Ballot
///
/// **Wire Format Decoupling**: This implementation allows the internal Ballot struct
/// to evolve freely while maintaining a stable wire format. The archived representation
/// uses fixed-size little-endian integers for cross-platform compatibility.
impl rkyv::Archive for Ballot {
    type Archived = ArchivedBallot;
    type Resolver = ();

    fn resolve(&self, _resolver: Self::Resolver, out: rkyv::Place<Self::Archived>) {
        use munge::munge;

        munge!(let ArchivedBallot { n, pid } = out);
        n.write(rkyv::rend::u64_le::from_native(self.n));
        pid.write(rkyv::rend::u64_le::from_native(self.pid));
    }
}

/// ## Implementation 2.9.2: Custom Serialize Implementation for Ballot
///
/// **Zero Dependencies**: Ballot serialization requires no additional data to be written,
/// making it extremely efficient for consensus operations.
impl<S: rkyv::rancor::Fallible + ?Sized> rkyv::Serialize<S> for Ballot {
    fn serialize(&self, _serializer: &mut S) -> Result<Self::Resolver, S::Error> {
        Ok(())
    }
}

/// ## Implementation 2.9.3: Custom Deserialize Implementation for Ballot
///
/// **Type Safety**: Deserialization preserves all type safety guarantees while
/// converting from the stable wire format back to the internal representation.
impl<D: rkyv::rancor::Fallible + ?Sized> rkyv::Deserialize<Ballot, D> for ArchivedBallot
where
    D::Error: rkyv::rancor::Source,
{
    fn deserialize(&self, _deserializer: &mut D) -> Result<Ballot, D::Error> {
        // Since we control the archived format, we know the data is valid
        // This should never fail for properly archived data
        Ok(Ballot {
            n: self.n(),
            pid: self.pid(),
        })
    }
}

/// ## Implementation 2.9.4: Custom Archive Implementation for ActorId
///
/// **String Stability**: Uses rkyv's stable string archiving to ensure the wire format
/// remains consistent even if the internal string representation changes.
impl rkyv::Archive for ActorId {
    type Archived = ArchivedActorId;
    type Resolver = rkyv::string::StringResolver;

    fn resolve(&self, resolver: Self::Resolver, out: rkyv::Place<Self::Archived>) {
        use munge::munge;

        munge!(let ArchivedActorId { id } = out);
        self.id.resolve(resolver, id);
    }
}

/// ## Implementation 2.9.5: Custom Serialize Implementation for ActorId
///
/// **String Serialization**: Delegates to rkyv's efficient string serialization
/// while maintaining our custom wire format structure.
impl<S> rkyv::Serialize<S> for ActorId
where
    S: rkyv::rancor::Fallible + rkyv::ser::Writer + ?Sized,
    S::Error: rkyv::rancor::Source,
{
    fn serialize(&self, serializer: &mut S) -> Result<Self::Resolver, S::Error> {
        self.id.serialize(serializer)
    }
}

/// ## Implementation 2.9.6: Custom Deserialize Implementation for ActorId
///
/// **Validation**: Deserializes through the builder pattern to ensure all validation
/// rules are applied, maintaining data integrity across the wire format boundary.
impl<D: rkyv::rancor::Fallible + ?Sized> rkyv::Deserialize<ActorId, D> for ArchivedActorId
where
    D::Error: rkyv::rancor::Source,
{
    fn deserialize(&self, deserializer: &mut D) -> Result<ActorId, D::Error> {
        let id_string: String = self.id.deserialize(deserializer)?;
        // Since we control the archived format, we know the data is valid
        // This should never fail for properly archived data
        Ok(ActorId { id: id_string })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_ballot_builder() {
        // Test successful construction
        let ballot = BallotBuilder::new()
            .n(42)
            .pid(123)
            .build()
            .expect("Should build successfully");

        assert_eq!(ballot.n(), 42);
        assert_eq!(ballot.pid(), 123);

        // Test missing fields
        let result = BallotBuilder::new().n(42).build();
        assert!(result.is_err());
    }

    #[test]
    fn test_actor_id_builder() {
        // Test successful construction
        let actor_id = ActorIdBuilder::new()
            .id("platform.com/context/actor")
            .build()
            .expect("Should build successfully");

        assert_eq!(actor_id.id_str(), "platform.com/context/actor");

        // Test child construction
        let child = ActorIdBuilder::child_of(&actor_id, "child_actor")
            .build()
            .expect("Should build child successfully");

        assert_eq!(child.id_str(), "platform.com/context/actor/child_actor");
        assert!(child.is_child_of(&actor_id));

        // Test empty ID
        let result = ActorIdBuilder::new().id("").build();
        assert!(result.is_err());

        // Test invalid formats
        assert!(ActorIdBuilder::new().id("no-dots").build().is_err()); // No domain
        assert!(ActorIdBuilder::new().id("domain.com//empty").build().is_err()); // Empty segment
        assert!(ActorIdBuilder::new().id("/starts-with-slash").build().is_err()); // Starts with slash
        assert!(ActorIdBuilder::new().id("ends-with-slash/").build().is_err()); // Ends with slash
    }

    #[test]
    fn test_schedule_builder() {
        // Test interval schedule
        let schedule = ScheduleBuilder::every(Duration::from_secs(60))
            .build()
            .expect("Should build successfully");

        match schedule {
            Schedule::Interval { every, .. } => {
                assert_eq!(every, Duration::from_secs(60));
            }
            _ => panic!("Expected interval schedule"),
        }

        // Test cron schedule
        let schedule = ScheduleBuilder::daily_at_hour(9)
            .build()
            .expect("Should build successfully");

        match schedule {
            Schedule::Cron { expression, .. } => {
                assert_eq!(expression, "0 9 * * *");
            }
            _ => panic!("Expected cron schedule"),
        }

        // Test invalid interval
        let result = ScheduleBuilder::every(Duration::ZERO).build();
        assert!(result.is_err());
    }

    #[test]
    fn test_schedule_timing_semantics() {
        // Test that demonstrates the leader-based timing approach

        // Create a schedule for "now" - captures current SystemTime
        let now_schedule = ScheduleBuilder::now().build().expect("Should build");

        // Create a schedule for future time
        let future_time = SystemTime::now() + Duration::from_secs(3600);
        let future_schedule = ScheduleBuilder::at(future_time).build().expect("Should build");

        // Create an interval schedule with relative timing
        let interval_schedule = ScheduleBuilder::every(Duration::from_secs(60))
            .build().expect("Should build");

        // Verify the schedule types are correct
        match now_schedule {
            Schedule::Once { at } => {
                // The 'at' time should be close to when we created it
                let elapsed = SystemTime::now().duration_since(at).unwrap_or(Duration::ZERO);
                assert!(elapsed < Duration::from_millis(100), "Schedule should be for recent time");
            }
            _ => panic!("Expected Once schedule"),
        }

        match future_schedule {
            Schedule::Once { at } => {
                // The 'at' time should be the exact time we specified
                assert_eq!(at, future_time);
            }
            _ => panic!("Expected Once schedule"),
        }

        match interval_schedule {
            Schedule::Interval { every, start_at, .. } => {
                assert_eq!(every, Duration::from_secs(60));
                assert!(start_at.is_none(), "Interval without start_at should be None");
            }
            _ => panic!("Expected Interval schedule"),
        }

        // Note: In a real implementation, these schedules would be:
        // 1. Stored in the event log via MemoryOperation::Schedule
        // 2. Read by the scheduler leader
        // 3. Triggered when the leader's clock >= the stored SystemTime
        // 4. This provides consistent triggering despite clock skew across nodes
    }

    #[test]
    fn test_wire_format_stability() {
        // Test that our custom rkyv implementations work correctly
        // and provide stable wire format

        // Test Ballot serialization/deserialization
        let ballot = BallotBuilder::new()
            .n(42)
            .pid(123)
            .build()
            .expect("Should build successfully");

        // Serialize using our custom implementation
        let ballot_bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&ballot)
            .expect("Should serialize ballot");

        // Deserialize using our custom implementation (unchecked for now)
        let archived_ballot: &ArchivedBallot = unsafe {
            rkyv::access_unchecked(&ballot_bytes)
        };

        // Verify the archived data
        assert_eq!(archived_ballot.n(), 42);
        assert_eq!(archived_ballot.pid(), 123);

        // Deserialize back to original type
        let deserialized_ballot: Ballot = rkyv::deserialize::<_, rkyv::rancor::Error>(archived_ballot)
            .expect("Should deserialize ballot");

        assert_eq!(deserialized_ballot.n(), 42);
        assert_eq!(deserialized_ballot.pid(), 123);

        // Test ActorId serialization/deserialization
        let actor_id = ActorIdBuilder::new()
            .id("platform.com/context/actor")
            .build()
            .expect("Should build successfully");

        // Serialize using our custom implementation
        let actor_id_bytes = rkyv::to_bytes::<rkyv::rancor::Error>(&actor_id)
            .expect("Should serialize actor ID");

        // Deserialize using our custom implementation (unchecked for now)
        let archived_actor_id: &ArchivedActorId = unsafe {
            rkyv::access_unchecked(&actor_id_bytes)
        };

        // Verify the archived data
        assert_eq!(archived_actor_id.id_str(), "platform.com/context/actor");
        assert_eq!(archived_actor_id.name(), "actor");

        // Test hierarchical operations on archived data
        let child = archived_actor_id.child("worker").expect("Should create child");
        assert_eq!(child.id_str(), "platform.com/context/actor/worker");

        // Deserialize back to original type
        let deserialized_actor_id: ActorId = rkyv::deserialize::<_, rkyv::rancor::Error>(archived_actor_id)
            .expect("Should deserialize actor ID");

        assert_eq!(deserialized_actor_id.id_str(), "platform.com/context/actor");
    }

    #[test]
    fn test_hierarchical_lifecycle_management() {
        // Test the new event-sourced hierarchical lifecycle approach

        // Create parent and child actors
        let parent_id = ActorIdBuilder::new()
            .id("platform.com/context/parent")
            .build()
            .expect("Should build parent ID");

        let child_id = ActorIdBuilder::child_of(&parent_id, "worker")
            .build()
            .expect("Should build child ID");

        // Verify hierarchical relationship
        assert!(child_id.is_child_of(&parent_id));
        assert_eq!(child_id.id_str(), "platform.com/context/parent/worker");

        // Test ActorChildrenState for tracking children
        let mut parent_state = ActorChildrenState::new();
        assert!(!parent_state.has_children());
        assert_eq!(parent_state.child_count(), 0);

        // Add child to parent's state (simulates MemoryOperation::ChildActorCreated)
        parent_state.add_child(child_id.clone());
        assert!(parent_state.has_children());
        assert_eq!(parent_state.child_count(), 1);
        assert!(parent_state.children().any(|id| id == &child_id));

        // Remove child from parent's state (simulates MemoryOperation::ChildActorTerminated)
        parent_state.remove_child(&child_id);
        assert!(!parent_state.has_children());
        assert_eq!(parent_state.child_count(), 0);

        // Test MemoryOperation variants for hierarchical lifecycle
        let create_op = MemoryOperation::ChildActorCreated {
            parent_id: parent_id.clone(),
            child_id: child_id.clone(),
        };

        let terminate_op = MemoryOperation::ChildActorTerminated {
            parent_id: parent_id.clone(),
            child_id: child_id.clone(),
        };

        // Verify the operations can be created and contain correct data
        match create_op {
            MemoryOperation::ChildActorCreated { parent_id: p, child_id: c } => {
                assert_eq!(p.id_str(), "platform.com/context/parent");
                assert_eq!(c.id_str(), "platform.com/context/parent/worker");
            }
            _ => panic!("Expected ChildActorCreated operation"),
        }

        match terminate_op {
            MemoryOperation::ChildActorTerminated { parent_id: p, child_id: c } => {
                assert_eq!(p.id_str(), "platform.com/context/parent");
                assert_eq!(c.id_str(), "platform.com/context/parent/worker");
            }
            _ => panic!("Expected ChildActorTerminated operation"),
        }

        // Test termination reasons
        let graceful = TerminationReason::Graceful;
        let crashed = TerminationReason::Crashed {
            error: "Division by zero".to_string()
        };
        let stopped_by_parent = TerminationReason::StoppedByParent;

        assert_eq!(graceful, TerminationReason::Graceful);
        match crashed {
            TerminationReason::Crashed { error } => {
                assert_eq!(error, "Division by zero");
            }
            _ => panic!("Expected Crashed termination reason"),
        }
        assert_eq!(stopped_by_parent, TerminationReason::StoppedByParent);

        // Note: In a real implementation, this demonstrates how:
        // 1. Parent actors track their children in their own state
        // 2. MemoryOperation::ChildActorCreated/Terminated manage the relationships
        // 3. Hierarchical stop propagates through parent -> children relationships
        // 4. No global actor registry is needed for cleanup
        // 5. The system is fully distributed and scalable
    }

    #[test]
    fn test_typed_identifiers() {
        // Test MemoryKey
        let memory_key = MemoryKey::new("user_data").expect("Should create memory key");
        assert_eq!(memory_key.as_str(), "user_data");
        assert!(MemoryKey::new("").is_err()); // Empty key should fail

        // Test ScheduleId
        let schedule_id = ScheduleId::new("daily_backup").expect("Should create schedule ID");
        assert_eq!(schedule_id.as_str(), "daily_backup");

        // Test local generation
        let generated_id = ScheduleId::generate();
        assert!(generated_id.as_str().starts_with("schedule_"));

        // Test distributed generation with node ID
        let distributed_id = ScheduleId::generate_with_node(42);
        assert!(distributed_id.as_str().starts_with("schedule_42_"));

        // Test VisualId
        let visual_id = VisualId::new("main_dashboard").expect("Should create visual ID");
        assert_eq!(visual_id.as_str(), "main_dashboard");

        // Test ActionTypeName
        let action_type = ActionTypeName::new("CreateUser").expect("Should create action type");
        assert_eq!(action_type.as_str(), "CreateUser");

        // Test RoleName
        let role = RoleName::new("admin").expect("Should create role name");
        assert_eq!(role.as_str(), "admin");

        // Test PermissionName
        let permission = PermissionName::new("read_users").expect("Should create permission");
        assert_eq!(permission.as_str(), "read_users");

        // Test PlatformDomain
        let domain = PlatformDomain::new("myapp.com").expect("Should create platform domain");
        assert_eq!(domain.as_str(), "myapp.com");

        // Test various invalid domain formats
        assert!(PlatformDomain::new("invalid").is_err()); // No dot
        assert!(PlatformDomain::new(".starts-with-dot").is_err()); // Starts with dot
        assert!(PlatformDomain::new("ends-with-dot.").is_err()); // Ends with dot
        assert!(PlatformDomain::new("double..dots").is_err()); // Consecutive dots
        assert!(PlatformDomain::new("has spaces.com").is_err()); // Contains spaces
        assert!(PlatformDomain::new("").is_err()); // Empty

        // Test ContextName
        let context = ContextName::new("user_service").expect("Should create context name");
        assert_eq!(context.as_str(), "user_service");

        // Test ValidationFunctionName
        let validation_fn = ValidationFunctionName::new("validate_email")
            .expect("Should create validation function name");
        assert_eq!(validation_fn.as_str(), "validate_email");
    }

    #[test]
    fn test_typed_payloads() {
        // Test SerializedReaction
        let reaction_data = vec![1, 2, 3, 4];
        let serialized_reaction = SerializedReaction::new(reaction_data.clone());
        assert_eq!(serialized_reaction.as_bytes(), &reaction_data);
        assert_eq!(serialized_reaction.len(), 4);
        assert!(!serialized_reaction.is_empty());

        // Test SerializedActorState
        let state_data = vec![5, 6, 7, 8];
        let serialized_state = SerializedActorState::new(state_data.clone());
        assert_eq!(serialized_state.as_bytes(), &state_data);
        assert_eq!(serialized_state.len(), 4);

        // Test MemoryValue
        let memory_data = vec![9, 10, 11, 12];
        let memory_value = MemoryValue::new(memory_data.clone());
        assert_eq!(memory_value.as_bytes(), &memory_data);
        assert_eq!(memory_value.len(), 4);

        // Test empty payloads
        let empty_reaction = SerializedReaction::new(vec![]);
        assert!(empty_reaction.is_empty());
        assert_eq!(empty_reaction.len(), 0);
    }

    #[test]
    fn test_resource_type() {
        // Test standard resource types
        assert_eq!(ResourceType::Memory.to_string(), "memory");
        assert_eq!(ResourceType::Cpu.to_string(), "cpu");
        assert_eq!(ResourceType::NetworkBandwidth.to_string(), "network_bandwidth");
        assert_eq!(ResourceType::DiskIo.to_string(), "disk_io");
        assert_eq!(ResourceType::ActorCount.to_string(), "actor_count");

        // Test custom resource type
        let custom = ResourceType::Custom("gpu".to_string());
        assert_eq!(custom.to_string(), "custom_gpu");

        // Test termination reason with typed resource
        let termination = TerminationReason::ResourceLimitExceeded {
            resource: ResourceType::Memory
        };
        match termination {
            TerminationReason::ResourceLimitExceeded { resource } => {
                assert_eq!(resource, ResourceType::Memory);
            }
            _ => panic!("Expected ResourceLimitExceeded"),
        }
    }

    #[test]
    fn test_memory_operations_with_typed_identifiers() {
        // Test that MemoryOperation now uses typed identifiers
        let memory_key = MemoryKey::new("test_key").expect("Should create key");
        let memory_value = MemoryValue::new(vec![1, 2, 3]);

        let set_op = MemoryOperation::Set {
            key: memory_key.clone(),
            value: memory_value,
        };

        match set_op {
            MemoryOperation::Set { key, value } => {
                assert_eq!(key.as_str(), "test_key");
                assert_eq!(value.as_bytes(), &[1, 2, 3]);
            }
            _ => panic!("Expected Set operation"),
        }

        // Test schedule operation with typed identifiers
        let schedule_id = ScheduleId::new("test_schedule").expect("Should create schedule ID");
        let reaction_bytes = SerializedReaction::new(vec![4, 5, 6]);
        let actor_id = ActorIdBuilder::new()
            .id("platform.com/context/actor")
            .build()
            .expect("Should build actor ID");

        let schedule_op = MemoryOperation::Schedule {
            schedule_id: schedule_id.clone(),
            reaction_bytes,
            reaction_type_name: "test::DailyBackupReaction".to_string(),
            schedule: Schedule::Once { at: SystemTime::now() },
            actor_id,
        };

        match schedule_op {
            MemoryOperation::Schedule { schedule_id: id, reaction_bytes, .. } => {
                assert_eq!(id.as_str(), "test_schedule");
                assert_eq!(reaction_bytes.as_bytes(), &[4, 5, 6]);
            }
            _ => panic!("Expected Schedule operation"),
        }

        // Test visual operation with typed identifier
        let visual_id = VisualId::new("test_visual").expect("Should create visual ID");
        let visual_def = VisualDefinition {
            tag: HtmlTag::Div,
            attributes: VisualAttributes::default(),
            children: vec![],
            events: VisualEvents::default(),
        };

        let visual_op = MemoryOperation::StoreVisual {
            visual_id: visual_id.clone(),
            definition: visual_def,
        };

        match visual_op {
            MemoryOperation::StoreVisual { visual_id: id, .. } => {
                assert_eq!(id.as_str(), "test_visual");
            }
            _ => panic!("Expected StoreVisual operation"),
        }
    }
}

// ============================================================================
// SECTION 3: TAMTIL MAIN ENTRY POINT AND PLATFORM MANAGEMENT
// ============================================================================

/// ## Definition 3.1: Main TAMTIL Entry Point
///
/// **Universal Access Pattern**: The main entry point that provides unified access
/// to all TAMTIL capabilities through the actor pattern. This is the only public
/// interface developers interact with.
///
/// **Design Principle**: Everything in TAMTIL is accessed through actors, ensuring
/// perfect isolation and consistent interaction patterns.
pub struct Tamtil {
    /// Actor registry for all actor operations
    actors: ActorRegistry,
    /// Platform configuration and management (private)
    platform: Platform,
}

impl Tamtil {
    /// Create and configure a new TAMTIL platform
    ///
    /// **Usage Pattern**: `Tamtil::platform().domain("myapp.com").build().await`
    pub fn platform() -> PlatformBuilder {
        PlatformBuilder::new()
    }

    /// Access the actor system - THE primary interaction pattern
    ///
    /// **Universal Pattern**: All functionality uses `tamtil.actors().actor(id).act(action)`
    pub fn actors(&self) -> &ActorRegistry {
        &self.actors
    }
}

/// ## Definition 3.2: Platform Builder Pattern
///
/// **Fluent Configuration**: Provides type-safe platform configuration with
/// hierarchical context and actor management.
///
/// **Pre-implemented Superpowers**: Platform and context actors come with built-in
/// capabilities for distributed systems, HTTP handling, and resource management.
pub struct PlatformBuilder {
    /// Platform domain (required)
    domain: Option<String>,
    /// Platform configuration
    config: PlatformConfig,
    /// Context configurations
    contexts: Vec<ContextConfig>,
}

/// Error type for platform construction
#[derive(Debug, thiserror::Error)]
#[non_exhaustive]
pub enum PlatformBuildError {
    #[error("Platform domain is required")]
    MissingDomain,
    #[error("Invalid domain format: {domain}")]
    InvalidDomain { domain: String },
    #[error("Context configuration error: {message}")]
    ContextError { message: String },
    #[error("Resource limit exceeded: {resource}")]
    ResourceLimitExceeded { resource: String },
}

impl PlatformBuilder {
    /// Create a new platform builder
    pub fn new() -> Self {
        Self {
            domain: None,
            config: PlatformConfig::default(),
            contexts: Vec::new(),
        }
    }

    /// Set the platform domain (required)
    ///
    /// **URL-based Addressing**: Forms the root of all actor IDs in the system
    pub fn domain(mut self, domain: impl Into<String>) -> Self {
        self.domain = Some(domain.into());
        self
    }

    /// Configure maximum actors across the entire platform
    pub fn max_actors(mut self, max: usize) -> Self {
        self.config.max_actors = max;
        self
    }

    /// Configure platform-wide memory limit in megabytes
    pub fn memory_limit_mb(mut self, limit_mb: usize) -> Self {
        self.config.memory_limit = Some(limit_mb * 1024 * 1024);
        self
    }

    /// Add a context with specific configuration
    ///
    /// **Context Actors**: Pre-implemented actors that manage hundreds of thousands
    /// of business logic actors with resource isolation and load balancing
    pub fn context(self, name: impl Into<String>) -> ContextBuilder {
        ContextBuilder::new(name.into(), self)
    }

    /// Build and start the platform
    pub async fn build(self) -> Result<Tamtil, PlatformBuildError> {
        let domain = self.domain.ok_or(PlatformBuildError::MissingDomain)?;

        // Validate domain format
        if domain.is_empty() || !domain.contains('.') {
            return Err(PlatformBuildError::InvalidDomain { domain });
        }

        // Build platform components (implementation would be in separate modules)
        let platform = Platform::new(domain, self.config).await
            .map_err(|_| PlatformBuildError::ContextError {
                message: "Failed to initialize platform".to_string()
            })?;

        let actors = ActorRegistry::new(platform.domain().to_string()).await
            .map_err(|_| PlatformBuildError::ContextError {
                message: "Failed to initialize actor registry".to_string()
            })?;

        Ok(Tamtil { actors, platform })
    }
}

impl Default for PlatformBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// ## Definition 3.3: Context Builder Pattern
///
/// **Hierarchical Management**: Contexts are specialized actors that manage
/// collections of business logic actors with resource isolation and scaling.
pub struct ContextBuilder {
    /// Context name
    name: String,
    /// Context configuration
    config: ContextConfig,
    /// Parent platform builder
    platform_builder: PlatformBuilder,
}

impl ContextBuilder {
    /// Create a new context builder
    fn new(name: String, platform_builder: PlatformBuilder) -> Self {
        Self {
            name,
            config: ContextConfig::default(),
            platform_builder,
        }
    }

    /// Configure maximum actors in this context
    pub fn max_actors(mut self, max: usize) -> Self {
        self.config.max_actors_per_context = Some(max);
        self
    }

    /// Configure memory limit for this context in megabytes
    pub fn memory_limit_mb(mut self, limit_mb: usize) -> Self {
        self.config.resource_limits.memory_limit = Some(limit_mb * 1024 * 1024);
        self
    }

    /// Configure CPU limit for this context
    pub fn cpu_limit(mut self, cpu_cores: f64) -> Self {
        self.config.resource_limits.cpu_limit = Some(cpu_cores);
        self
    }

    /// Return to platform builder to add more contexts or build
    pub fn platform(mut self) -> PlatformBuilder {
        self.platform_builder.contexts.push(self.config);
        self.platform_builder
    }

    /// Build the platform with this context (convenience method)
    pub async fn build(self) -> Result<Tamtil, PlatformBuildError> {
        self.platform().build().await
    }
}

// ============================================================================
// SECTION 4: ACTOR REGISTRY AND INTERACTION PATTERNS
// ============================================================================

/// ## Definition 4.1: Actor Registry
///
/// **Universal Actor Access**: Manages all actors in the system with URL-based
/// addressing. Provides the primary interaction pattern for all TAMTIL operations.
///
/// **Hierarchical Management**: Supports platform/context/actor hierarchy with
/// automatic lifecycle management and resource isolation.
pub struct ActorRegistry {
    /// Platform domain for URL-based actor IDs
    domain: String,
    /// Internal actor management (implementation details hidden)
    _internal: ActorRegistryInternal,
}

impl ActorRegistry {
    /// Create a new actor registry for the given domain
    pub(crate) async fn new(domain: String) -> TamtilResult<Self> {
        Ok(Self {
            domain,
            _internal: ActorRegistryInternal::new().await?,
        })
    }

    /// Get an actor proxy for the given actor ID
    ///
    /// **Universal Pattern**: This is the primary way to interact with any actor
    /// **Usage**: `tamtil.actors().actor("platform.com/context/actor_name").act(action)`
    pub fn actor(&self, actor_id: impl Into<ActorId>) -> ActorProxy {
        let actor_id = actor_id.into();
        ActorProxy::new(actor_id, self)
    }

    /// Get the platform domain
    pub fn domain(&self) -> &str {
        &self.domain
    }
}

/// ## Definition 4.2: Actor Proxy
///
/// **Type-Safe Actor Interaction**: Provides the `act(action)` interface with
/// compile-time type safety and runtime validation.
///
/// **Transparent Distribution**: Handles local and remote actor communication
/// transparently through the same interface.
pub struct ActorProxy<'a> {
    /// Target actor ID
    actor_id: ActorId,
    /// Reference to actor registry
    registry: &'a ActorRegistry,
}

impl<'a> ActorProxy<'a> {
    /// Create a new actor proxy
    fn new(actor_id: ActorId, registry: &'a ActorRegistry) -> Self {
        Self { actor_id, registry }
    }

    /// Get the actor ID
    pub fn actor_id(&self) -> &ActorId {
        &self.actor_id
    }

    /// Execute an action on this actor - THE primary interaction pattern
    ///
    /// **Universal Interface**: All functionality in TAMTIL uses this pattern
    /// **Type Safety**: Action and Response types are validated at compile time
    /// **Validation**: Action validation rules are executed before processing
    pub async fn act<A: Action>(&self, action: A) -> TamtilResult<A::Response> {
        // Implementation would handle:
        // 1. Action validation through action.validate()
        // 2. Routing to local or remote actor
        // 3. Serialization/deserialization with rkyv
        // 4. Error handling and retries
        todo!("ActorProxy::act implementation")
    }
}

/// Internal actor registry implementation (hidden from developers)
struct ActorRegistryInternal {
    // Implementation details hidden
}

impl ActorRegistryInternal {
    async fn new() -> TamtilResult<Self> {
        Ok(Self {})
    }
}

// ============================================================================
// SECTION 5: PLATFORM AND CONTEXT CONFIGURATION
// ============================================================================

/// ## Definition 5.1: Platform Configuration
///
/// **Resource Management**: Defines platform-wide resource limits and policies
/// for distributed actor system management.
#[derive(Debug, Clone)]
pub struct PlatformConfig {
    /// Maximum number of actors across the entire platform
    pub max_actors: usize,
    /// Platform-wide memory limit in bytes
    pub memory_limit: Option<usize>,
    /// Consensus algorithm configuration
    pub consensus: ConsensusConfig,
    /// Network and transport configuration
    pub network: NetworkConfig,
}

impl Default for PlatformConfig {
    fn default() -> Self {
        Self {
            max_actors: 100_000,
            memory_limit: None,
            consensus: ConsensusConfig::default(),
            network: NetworkConfig::default(),
        }
    }
}

/// ## Definition 5.2: Context Configuration
///
/// **Isolation Boundaries**: Defines resource limits and policies for context
/// actors that manage collections of business logic actors.
#[derive(Debug, Clone)]
pub struct ContextConfig {
    /// Context name for URL-based addressing
    pub name: String,
    /// Maximum actors per context
    pub max_actors_per_context: Option<usize>,
    /// Resource limits for this context
    pub resource_limits: ResourceLimits,
    /// Actor lifecycle configuration
    pub lifecycle: LifecycleConfig,
}

impl Default for ContextConfig {
    fn default() -> Self {
        Self {
            name: "default".to_string(),
            max_actors_per_context: Some(10_000),
            resource_limits: ResourceLimits::default(),
            lifecycle: LifecycleConfig::default(),
        }
    }
}

/// ## Definition 5.3: Resource Limits
///
/// **Resource Isolation**: Defines computational and memory limits for
/// context-level resource management and isolation.
#[derive(Debug, Clone)]
pub struct ResourceLimits {
    /// Memory limit in bytes
    pub memory_limit: Option<usize>,
    /// CPU limit in cores (fractional allowed)
    pub cpu_limit: Option<f64>,
    /// Network bandwidth limit in bytes per second
    pub network_limit: Option<usize>,
    /// Disk I/O limit in bytes per second
    pub disk_io_limit: Option<usize>,
}

impl Default for ResourceLimits {
    fn default() -> Self {
        Self {
            memory_limit: None,
            cpu_limit: None,
            network_limit: None,
            disk_io_limit: None,
        }
    }
}

/// ## Definition 5.4: Consensus Configuration
///
/// **Distributed Consistency**: Configuration for consensus algorithms used
/// in distributed actor coordination and state synchronization.
#[derive(Debug, Clone)]
pub struct ConsensusConfig {
    /// Consensus algorithm type
    pub algorithm: ConsensusAlgorithm,
    /// Replication factor for fault tolerance
    pub replication_factor: usize,
    /// Timeout for consensus operations
    pub timeout: Duration,
    /// Batch size for consensus operations
    pub batch_size: usize,
}

impl Default for ConsensusConfig {
    fn default() -> Self {
        Self {
            algorithm: ConsensusAlgorithm::OmniPaxos,
            replication_factor: 3,
            timeout: Duration::from_secs(5),
            batch_size: 100,
        }
    }
}

/// ## Definition 5.5: Consensus Algorithm Types
///
/// **Algorithm Selection**: Supported consensus algorithms for different
/// consistency and performance requirements.
#[derive(Debug, Clone, PartialEq, Eq)]
#[non_exhaustive]
pub enum ConsensusAlgorithm {
    /// OmniPaxos for high-performance consensus
    OmniPaxos,
    /// Raft for simplicity and understanding
    Raft,
    /// PBFT for Byzantine fault tolerance
    Pbft,
}

/// ## Definition 5.6: Network Configuration
///
/// **Transport Layer**: Configuration for network communication between
/// distributed actor system nodes.
#[derive(Debug, Clone)]
pub struct NetworkConfig {
    /// HTTP/3 transport configuration
    pub http3: Http3Config,
    /// TLS configuration for secure communication
    pub tls: TlsConfig,
    /// Connection pooling and management
    pub connection: ConnectionConfig,
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            http3: Http3Config::default(),
            tls: TlsConfig::default(),
            connection: ConnectionConfig::default(),
        }
    }
}

/// ## Definition 5.7: HTTP/3 Configuration
///
/// **Modern Transport**: Configuration for HTTP/3 with QUIC for high-performance
/// actor communication with multiplexing and low latency.
#[derive(Debug, Clone)]
pub struct Http3Config {
    /// Maximum concurrent streams per connection
    pub max_concurrent_streams: usize,
    /// Connection idle timeout
    pub idle_timeout: Duration,
    /// Maximum packet size
    pub max_packet_size: usize,
}

impl Default for Http3Config {
    fn default() -> Self {
        Self {
            max_concurrent_streams: 1000,
            idle_timeout: Duration::from_secs(30),
            max_packet_size: 1350,
        }
    }
}

/// ## Definition 5.8: TLS Configuration
///
/// **Secure Communication**: TLS configuration for encrypted actor communication
/// across network boundaries with certificate management.
#[derive(Debug, Clone)]
pub struct TlsConfig {
    /// Certificate file path
    pub cert_path: Option<String>,
    /// Private key file path
    pub key_path: Option<String>,
    /// CA certificate for client verification
    pub ca_cert_path: Option<String>,
    /// Require client certificates
    pub require_client_cert: bool,
}

impl Default for TlsConfig {
    fn default() -> Self {
        Self {
            cert_path: None,
            key_path: None,
            ca_cert_path: None,
            require_client_cert: false,
        }
    }
}

/// ## Definition 5.9: Connection Configuration
///
/// **Connection Management**: Configuration for connection pooling, timeouts,
/// and retry policies in distributed actor communication.
#[derive(Debug, Clone)]
pub struct ConnectionConfig {
    /// Maximum connections per node
    pub max_connections_per_node: usize,
    /// Connection timeout
    pub connect_timeout: Duration,
    /// Request timeout
    pub request_timeout: Duration,
    /// Retry policy configuration
    pub retry: RetryConfig,
}

impl Default for ConnectionConfig {
    fn default() -> Self {
        Self {
            max_connections_per_node: 100,
            connect_timeout: Duration::from_secs(5),
            request_timeout: Duration::from_secs(30),
            retry: RetryConfig::default(),
        }
    }
}

/// ## Definition 5.10: Retry Configuration
///
/// **Fault Tolerance**: Configuration for retry policies in distributed
/// actor communication with exponential backoff and circuit breaker patterns.
#[derive(Debug, Clone)]
pub struct RetryConfig {
    /// Maximum number of retries
    pub max_retries: usize,
    /// Initial retry delay
    pub initial_delay: Duration,
    /// Maximum retry delay
    pub max_delay: Duration,
    /// Backoff multiplier
    pub backoff_multiplier: f64,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            initial_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(10),
            backoff_multiplier: 2.0,
        }
    }
}

/// ## Definition 5.11: Lifecycle Configuration
///
/// **Actor Lifecycle**: Configuration for actor startup, shutdown, and
/// health monitoring with automatic recovery policies.
#[derive(Debug, Clone)]
pub struct LifecycleConfig {
    /// Startup timeout
    pub startup_timeout: Duration,
    /// Shutdown timeout
    pub shutdown_timeout: Duration,
    /// Health check interval
    pub health_check_interval: Duration,
    /// Restart policy
    pub restart_policy: RestartPolicy,
}

impl Default for LifecycleConfig {
    fn default() -> Self {
        Self {
            startup_timeout: Duration::from_secs(30),
            shutdown_timeout: Duration::from_secs(10),
            health_check_interval: Duration::from_secs(60),
            restart_policy: RestartPolicy::OnFailure,
        }
    }
}

/// ## Definition 5.12: Restart Policy
///
/// **Fault Recovery**: Policies for automatic actor restart on failure
/// with different strategies for different failure modes.
#[derive(Debug, Clone, PartialEq)]
#[non_exhaustive]
pub enum RestartPolicy {
    /// Never restart actors
    Never,
    /// Restart only on failure
    OnFailure,
    /// Always restart actors
    Always,
    /// Restart with exponential backoff
    ExponentialBackoff {
        /// Initial delay before restart
        initial_delay: Duration,
        /// Maximum delay between restarts
        max_delay: Duration,
        /// Backoff multiplier
        multiplier: f64,
    },
}

// ============================================================================
// SECTION 6: MEMORY SYSTEM INTERFACES
// ============================================================================

/// ## Definition 6.1: Actor-Scoped Memory Interface Implementation
///
/// **Perfect Isolation**: Memory interface that is automatically scoped to the
/// calling actor. Only accessible within actor.act() methods.
///
/// **Event Sourcing**: All state changes go through the remember/recall pattern
/// with automatic persistence and distribution.
impl ActorMemories {
    /// Create actor-scoped memories (internal use only)
    pub(crate) fn new(actor_id: ActorId, system: MemorySystemHandle) -> Self {
        // Implementation would initialize with proper scoping
        todo!("ActorMemories::new implementation")
    }

    /// Remember a reaction (write state) - automatically scoped to this actor
    ///
    /// **Event Sourcing**: All state changes go through reactions for auditability
    /// **Atomicity**: All memory operations in a reaction succeed or fail together
    pub async fn remember<R: Reaction>(&self, _reaction: R) -> TamtilResult<()> {
        // Implementation would:
        // 1. Validate reaction through reaction.validate()
        // 2. Apply memory operations atomically
        // 3. Trigger subscriptions and scheduled reactions
        // 4. Replicate across cluster nodes
        todo!("ActorMemories::remember implementation")
    }

    /// Recall data (read state) - automatically scoped to this actor
    ///
    /// **Unified Interface**: Works for both raw data and visual memories
    /// **Zero-Copy**: Uses rkyv for efficient data access without deserialization
    pub async fn recall<Q: Query>(&self, _query: Q) -> TamtilResult<Q::Response> {
        // Implementation would:
        // 1. Execute query against actor-scoped data
        // 2. Return zero-copy archived data when possible
        // 3. Handle time travel queries for historical data
        todo!("ActorMemories::recall implementation")
    }

    /// Get the actor ID this memory interface is scoped to
    pub fn actor_id(&self) -> &ActorId {
        // Implementation would return the actor ID
        todo!("ActorMemories::actor_id implementation")
    }
}

/// Internal memory system handle (hidden from developers)
struct MemorySystemHandle {
    // Implementation details hidden
}

/// ## Definition 6.2: Query Trait
///
/// **Type-Safe Queries**: Trait for defining type-safe memory queries with
/// compile-time response type validation.
///
/// **Zero-Copy Access**: Designed to work with rkyv archived data for maximum
/// performance in distributed systems.
#[async_trait]
pub trait Query: Send + Sync + 'static {
    /// Response type for this query
    type Response: Send + Sync + 'static;

    /// Execute the query against actor-scoped archived data
    ///
    /// **Performance**: Operates directly on rkyv archived data when possible
    /// **Scoping**: Automatically limited to calling actor's data
    async fn execute(&self, data: &ArchivedMemoryData) -> TamtilResult<Self::Response>;
}

/// ## Definition 6.3: Archived Memory Data
///
/// **Zero-Copy Storage**: Represents archived memory data that can be accessed
/// without deserialization for maximum performance.
///
/// **Type Safety**: Maintains type safety while providing zero-copy access
/// to stored actor memories.
pub struct ArchivedMemoryData {
    /// Raw archived data
    _data: Vec<u8>,
    /// Type information for safe access
    _type_info: MemoryTypeInfo,
}

impl ArchivedMemoryData {
    /// Access archived data by key with type safety
    pub fn get<T>(&self, _key: &str) -> TamtilResult<Option<&T::Archived>>
    where
        T: rkyv::Archive,
        T::Archived: rkyv::traits::Portable,
    {
        // Implementation would safely access archived data
        todo!("ArchivedMemoryData::get implementation")
    }

    /// Get all keys in the archived data
    pub fn keys(&self) -> impl Iterator<Item = &str> {
        // Implementation would return iterator over keys
        std::iter::empty()
    }
}

/// Memory type information for safe archived data access
struct MemoryTypeInfo {
    // Implementation details hidden
}

// ============================================================================
// SECTION 7: VISUAL SYSTEM INTERFACES
// ============================================================================

/// ## Definition 7.1: Visual Definition
///
/// **Reactive UI**: Represents visual components that are stored as special
/// memories and automatically synchronized with connected clients.
///
/// **Builder Pattern**: Uses fluent interface for constructing complex visual
/// hierarchies with type safety and validation.
#[derive(Debug, Clone)]
pub struct VisualDefinition {
    /// HTML tag type
    tag: HtmlTag,
    /// Element attributes
    attributes: VisualAttributes,
    /// Child elements
    children: Vec<VisualDefinition>,
    /// Event handlers
    events: VisualEvents,
}

/// ## Definition 7.2: HTML Tag Types
///
/// **Type Safety**: Enumeration of supported HTML tags with compile-time
/// validation of valid tag usage and nesting rules.
#[derive(Debug, Clone, PartialEq, Eq)]
#[non_exhaustive]
pub enum HtmlTag {
    /// Document structure
    Html, Head, Body, Title, Meta,
    /// Content sectioning
    Header, Nav, Main, Section, Article, Aside, Footer,
    /// Text content
    H1, H2, H3, H4, H5, H6, P, Blockquote, Pre, Code,
    /// Inline text
    Span, A, Strong, Em, Mark, Small, Del, Ins,
    /// Lists
    Ul, Ol, Li, Dl, Dt, Dd,
    /// Forms
    Form, Input, Textarea, Select, Option, Button, Label,
    /// Tables
    Table, Thead, Tbody, Tfoot, Tr, Th, Td,
    /// Media
    Img, Video, Audio, Source, Canvas, Svg,
    /// Generic containers
    Div,
}

/// ## Definition 7.3: Visual Attributes
///
/// **Type-Safe Attributes**: Structured representation of HTML attributes
/// with validation and automatic escaping for security.
#[derive(Debug, Clone, Default)]
pub struct VisualAttributes {
    /// Element ID
    pub id: Option<String>,
    /// CSS classes
    pub classes: Vec<String>,
    /// Inline styles
    pub styles: std::collections::HashMap<String, String>,
    /// Data attributes
    pub data: std::collections::HashMap<String, String>,
    /// Other attributes
    pub other: std::collections::HashMap<String, String>,
}

/// ## Definition 7.4: Visual Events
///
/// **Event Handling**: Configuration for client-side event handling with
/// server-side action routing through the actor system.
#[derive(Debug, Clone, Default)]
pub struct VisualEvents {
    /// Click event configuration
    pub click: Option<VisualEventConfig>,
    /// Input event configuration
    pub input: Option<VisualEventConfig>,
    /// Submit event configuration
    pub submit: Option<VisualEventConfig>,
    /// Custom events
    pub custom: std::collections::HashMap<String, VisualEventConfig>,
}

/// ## Definition 7.5: Visual Event Configuration
///
/// **Actor Integration**: Configuration for routing client-side events to
/// server-side actors through the universal actor pattern.
#[derive(Debug, Clone)]
pub struct VisualEventConfig {
    /// Target actor ID for the event
    pub target_actor: ActorId,
    /// Action to send to the target actor
    pub action_type: ActionTypeName,
    /// Additional event data
    pub data: std::collections::HashMap<String, String>,
    /// Prevent default browser behavior
    pub prevent_default: bool,
    /// Stop event propagation
    pub stop_propagation: bool,
}

// ============================================================================
// SECTION 8: PLATFORM AND VALIDATION TYPES
// ============================================================================

/// ## Definition 8.1: Platform
///
/// **System Foundation**: Represents the core platform that manages the entire
/// distributed actor system with consensus, networking, and resource management.
///
/// **Implementation Hidden**: Internal implementation details are not exposed
/// to developers who interact only through the actor pattern.
pub struct Platform {
    /// Platform domain
    domain: String,
    /// Platform configuration
    config: PlatformConfig,
    /// Internal platform state (hidden)
    _internal: PlatformInternal,
}

impl Platform {
    /// Create a new platform (internal use only)
    pub(crate) async fn new(domain: String, config: PlatformConfig) -> TamtilResult<Self> {
        Ok(Self {
            domain,
            config,
            _internal: PlatformInternal::new().await?,
        })
    }

    /// Get the platform domain
    pub fn domain(&self) -> &str {
        &self.domain
    }

    /// Get the platform configuration
    pub fn config(&self) -> &PlatformConfig {
        &self.config
    }
}

/// Internal platform implementation (hidden from developers)
struct PlatformInternal {
    // Implementation details hidden
}

impl PlatformInternal {
    async fn new() -> TamtilResult<Self> {
        Ok(Self {})
    }
}

/// ## Definition 8.2: Validation Operation
///
/// **Security and Integrity**: Represents validation operations that are
/// executed by the system before processing actions and reactions.
///
/// **Declarative Validation**: Actions and reactions declare their validation
/// requirements, and the system executes them transparently.
#[derive(Debug, Clone)]
#[non_exhaustive]
pub enum ValidationOperation {
    /// Require specific role for authorization
    RequireRole {
        /// Required role name
        role: RoleName,
        /// Resource being accessed
        resource: String,
    },
    /// Require specific permission
    RequirePermission {
        /// Permission name
        permission: PermissionName,
        /// Resource being accessed
        resource: String,
    },
    /// Validate data format
    ValidateFormat {
        /// Field name
        field: String,
        /// Expected format pattern
        pattern: String,
    },
    /// Validate data range
    ValidateRange {
        /// Field name
        field: String,
        /// Minimum value
        min: Option<f64>,
        /// Maximum value
        max: Option<f64>,
    },
    /// Custom validation function
    Custom {
        /// Validation function name
        function: ValidationFunctionName,
        /// Validation parameters
        params: std::collections::HashMap<String, String>,
    },
}

/// ## Definition 8.3: Action Context
///
/// **Execution Context**: Provides context information for action execution
/// including caller chain, timestamps, and security context.
///
/// **Audit Trail**: Automatically maintained by the system for security
/// and debugging purposes.
#[derive(Debug, Clone)]
pub struct ActionContext {
    /// Type of action being executed
    pub action_type: ActionTypeName,
    /// Chain of actors that led to this action
    pub caller_chain: Vec<ActorId>,
    /// Original caller that initiated the action chain
    pub original_caller: Option<ActorId>,
    /// Immediate caller of this action
    pub immediate_caller: ActorId,
    /// Timestamp when action was initiated
    pub timestamp: SystemTime,
    /// Security context for authorization
    pub security_context: SecurityContext,
}

/// ## Definition 8.4: Security Context
///
/// **Authorization Information**: Contains user identity, roles, and permissions
/// for authorization decisions in action and reaction validation.
///
/// **OAuth/OIDC Integration**: Designed to work with standard authentication
/// and authorization protocols.
#[derive(Debug, Clone)]
pub struct SecurityContext {
    /// User identity (if authenticated)
    pub user_id: Option<String>,
    /// User roles
    pub roles: Vec<RoleName>,
    /// User permissions
    pub permissions: Vec<PermissionName>,
    /// Authentication token
    pub token: Option<String>,
    /// Token expiration time
    pub token_expires_at: Option<SystemTime>,
}

impl Default for SecurityContext {
    fn default() -> Self {
        Self {
            user_id: None,
            roles: Vec::new(),
            permissions: Vec::new(),
            token: None,
            token_expires_at: None,
        }
    }
}

/// ## Definition 8.5: Actor Context
///
/// **Actor Information**: Provides context about the actor including its
/// position in the hierarchy and resource constraints.
///
/// **Hierarchical Awareness**: Enables actors to understand their place
/// in the platform/context/actor hierarchy.
#[derive(Debug, Clone)]
pub struct ActorContext {
    /// This actor's ID
    pub actor_id: ActorId,
    /// Context this actor belongs to
    pub context_id: ActorId,
    /// Platform this actor is running on
    pub platform_domain: PlatformDomain,
    /// Resource limits for this actor
    pub resource_limits: ResourceLimits,
    /// Actor lifecycle state
    pub lifecycle_state: ActorLifecycleState,
}

/// ## Definition 8.6: Actor Lifecycle State
///
/// **State Management**: Represents the current lifecycle state of an actor
/// for proper state transitions and resource management.
#[derive(Debug, Clone, PartialEq, Eq)]
#[non_exhaustive]
pub enum ActorLifecycleState {
    /// Actor is being initialized
    Initializing,
    /// Actor is starting up
    Starting,
    /// Actor is running and ready to process actions
    Running,
    /// Actor is stopping gracefully
    Stopping,
    /// Actor has stopped
    Stopped,
    /// Actor has failed and needs recovery
    Failed {
        /// Error that caused the failure
        error: String,
        /// Timestamp of failure
        failed_at: SystemTime,
    },
    /// Actor is being restarted
    Restarting,
}

/// ## Definition 8.7: Common Response Types
///
/// **Standard Responses**: Common response types that can be used by actors
/// for standard operations like CRUD and status reporting.
///
/// **HTTP Integration**: Designed to map naturally to HTTP status codes
/// for web-based actor interactions.
#[derive(Debug, Clone)]
#[non_exhaustive]
pub enum CommonResponse {
    /// Operation completed successfully
    Success,
    /// Resource was created successfully
    Created {
        /// ID of created resource
        id: String,
        /// Location of created resource
        location: Option<String>,
    },
    /// Resource was updated successfully
    Updated {
        /// ID of updated resource
        id: String,
        /// Version of updated resource
        version: Option<String>,
    },
    /// Resource was deleted successfully
    Deleted {
        /// ID of deleted resource
        id: String,
    },
    /// Requested resource was not found
    NotFound {
        /// ID of requested resource
        id: String,
    },
    /// Request was invalid
    BadRequest {
        /// Error message
        message: String,
        /// Field-specific errors
        field_errors: std::collections::HashMap<String, String>,
    },
    /// User is not authorized
    Unauthorized {
        /// Error message
        message: String,
    },
    /// User is forbidden from accessing resource
    Forbidden {
        /// Error message
        message: String,
        /// Required permission
        required_permission: Option<String>,
    },
    /// Internal server error
    InternalError {
        /// Error message
        message: String,
        /// Error code for debugging
        error_code: Option<String>,
    },
    /// Visual response for UI updates
    Visual {
        /// Visual definition
        definition: VisualDefinition,
        /// Target element for update
        target: Option<String>,
    },
}

// ============================================================================
// SECTION 9: PRELUDE MODULE
// ============================================================================

/// ## Prelude Module
///
/// **Convenient Imports**: Re-exports all commonly used types and traits
/// for easy importing in user code.
///
/// **Usage**: `use tamtil_core::prelude::*;`
pub mod prelude {
    pub use super::{
        // Core types
        ActorId, ActorIdBuilder, ActorIdBuildError,
        Ballot, BallotBuilder, BallotBuildError,
        Schedule, ScheduleBuilder, ScheduleBuildError,
        TamtilError, TamtilResult,
        MemoryOperation,

        // Typed identifiers for enhanced type safety
        MemoryKey, ScheduleId, VisualId, ActionTypeName,
        RoleName, PermissionName, PlatformDomain, ContextName,
        ValidationFunctionName,

        // Typed payloads for enhanced clarity
        SerializedReaction, SerializedActorState, MemoryValue,

        // Resource and configuration types
        ResourceType,

        // Main entry point
        Tamtil,

        // Platform and configuration
        PlatformBuilder, PlatformBuildError,
        PlatformConfig, ContextConfig, ResourceLimits,
        ConsensusConfig, ConsensusAlgorithm,
        NetworkConfig, Http3Config, TlsConfig, ConnectionConfig,
        RetryConfig, LifecycleConfig, RestartPolicy,

        // Actor system
        ActorRegistry, ActorProxy,
        ActorMemories, Context, ActionBuilder,

        // Traits
        Action, Reaction, ScheduledReaction, RemoteReaction, Actor, Query,

        // Memory and visual system
        ArchivedMemoryData, VisualDefinition, HtmlTag,
        VisualAttributes, VisualEvents, VisualEventConfig,

        // Context and validation
        ActionContext, SecurityContext, ActorContext,
        ActorLifecycleState, ValidationOperation,
        CommonResponse,
    };
}

// ============================================================================
// SECTION 10: MATHEMATICAL PROPERTIES AND GUARANTEES
// ============================================================================

//! ## Mathematical Properties of TAMTIL Core Types
//!
//! This section documents the mathematical properties and guarantees that the TAMTIL
//! type system enables, rather than properties inherent to the types themselves.
//!
//! ### 10.1 Ballot Ordering Properties
//!
//! **Total Ordering**: The Ballot type with its (n, pid) structure and lexicographic
//! ordering provides a total order over all ballots in the system. For any two ballots
//! B1 = (n1, pid1) and B2 = (n2, pid2), exactly one of the following holds:
//! - B1 < B2 (if n1 < n2, or n1 = n2 and pid1 < pid2)
//! - B1 > B2 (if n1 > n2, or n1 = n2 and pid1 > pid2)
//! - B1 = B2 (if n1 = n2 and pid1 = pid2)
//!
//! This total ordering is essential for consensus algorithms like Paxos.
//!
//! ### 10.2 Actor Hierarchy Properties
//!
//! **Tree Structure**: ActorId with its hierarchical format ensures that actors
//! form a tree structure where each actor has at most one parent.
//!
//! **Transitive Cleanup**: The parent-child relationship enables transitive cleanup
//! where stopping a parent automatically stops all descendants.
//!
//! ### 10.3 Event Sourcing Properties
//!
//! **State Reconstruction**: The MemoryOperation sequence enables complete state
//! reconstruction from the event log, providing audit trails and time-travel queries.
//!
//! **Deterministic Replay**: Given the same sequence of MemoryOperations, the system
//! will reach the same state, enabling distributed replication.
//!
//! ### 10.4 Type Safety Properties
//!
//! **Compile-Time Safety**: The type system prevents many classes of errors at
//! compile time, including:
//! - Mixing different kinds of identifiers (MemoryKey vs ScheduleId)
//! - Type mismatches in action-response pairs
//! - Invalid memory operation construction
//!
//! **Wire Format Stability**: Custom rkyv implementations ensure that internal
//! type changes don't break serialized data compatibility.
//!
//! ### 10.5 Distributed Consistency Foundations
//!
//! **Event Ordering**: The combination of consensus (via Ballot ordering) and
//! event sourcing (via MemoryOperation sequences) provides the foundation for
//! achieving distributed consistency.
//!
//! **Causal Consistency**: The hierarchical actor model with parent-child
//! relationships enables causal consistency within actor hierarchies.
//!
//! **Note**: These properties describe what the TAMTIL system aims to achieve
//! using these types, not guarantees provided by the types alone.


